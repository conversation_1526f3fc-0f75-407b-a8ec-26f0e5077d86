"""
Centralized logging configuration for the application
"""
import logging
import os
from datetime import datetime
from typing import Optional


def setup_logging(log_level: str = "INFO", log_file: Optional[str] = None) -> logging.Logger:
    """
    Setup centralized logging configuration
    
    Args:
        log_level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_file: Optional log file path. If None, uses default location
        
    Returns:
        Configured logger instance
    """
    # Create logger
    logger = logging.getLogger('airchivist')
    logger.setLevel(getattr(logging, log_level.upper()))
    
    # Clear existing handlers to avoid duplicates
    logger.handlers.clear()
    
    # Create formatter
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s'
    )
    
    # Console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(getattr(logging, log_level.upper()))
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # File handler
    if log_file is None:
        # Default log file location
        project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
        log_dir = os.path.join(project_root, 'data', 'logs')
        os.makedirs(log_dir, exist_ok=True)
        log_file = os.path.join(log_dir, f'airchivist_{datetime.now().strftime("%Y%m%d")}.log')
    
    file_handler = logging.FileHandler(log_file)
    file_handler.setLevel(logging.DEBUG)  # Always log everything to file
    file_handler.setFormatter(formatter)
    logger.addHandler(file_handler)
    
    return logger


def get_logger(name: str = 'airchivist') -> logging.Logger:
    """
    Get a logger instance
    
    Args:
        name: Logger name
        
    Returns:
        Logger instance
    """
    return logging.getLogger(name)


# Standard error response format
def create_error_response(message: str, status_code: int = 500, details: Optional[dict] = None) -> dict:
    """
    Create standardized error response
    
    Args:
        message: Error message
        status_code: HTTP status code
        details: Optional additional error details
        
    Returns:
        Standardized error response dictionary
    """
    response = {
        'error': message,
        'status_code': status_code,
        'timestamp': datetime.utcnow().isoformat()
    }
    
    if details:
        response['details'] = details
        
    return response


def log_and_create_error_response(
    logger: logging.Logger, 
    message: str, 
    exception: Optional[Exception] = None,
    status_code: int = 500,
    details: Optional[dict] = None
) -> dict:
    """
    Log error and create standardized error response
    
    Args:
        logger: Logger instance
        message: Error message
        exception: Optional exception object
        status_code: HTTP status code
        details: Optional additional error details
        
    Returns:
        Standardized error response dictionary
    """
    if exception:
        logger.error(f"{message}: {str(exception)}", exc_info=True)
    else:
        logger.error(message)
    
    return create_error_response(message, status_code, details)
