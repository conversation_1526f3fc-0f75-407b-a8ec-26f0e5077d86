import { useState } from 'react'
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Checkbox } from '@/components/ui/checkbox'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  Upload, 
  FolderOpen, 
  FileText, 
  Plus, 
  X, 
  CheckCircle,
  AlertCircle,
  Sparkles
} from 'lucide-react'
import '../App.css'

const ImportDialog = ({ onImportComplete }) => {
  const [open, setOpen] = useState(false)
  const [filePaths, setFilePaths] = useState([''])
  const [directoryPaths, setDirectoryPaths] = useState([''])
  const [processWithLLM, setProcessWithLLM] = useState(true)
  const [recursive, setRecursive] = useState(true)
  const [importing, setImporting] = useState(false)
  const [importResults, setImportResults] = useState(null)

  const addFilePath = () => {
    setFilePaths([...filePaths, ''])
  }

  const removeFilePath = (index) => {
    setFilePaths(filePaths.filter((_, i) => i !== index))
  }

  const updateFilePath = (index, value) => {
    const newPaths = [...filePaths]
    newPaths[index] = value
    setFilePaths(newPaths)
  }

  const addDirectoryPath = () => {
    setDirectoryPaths([...directoryPaths, ''])
  }

  const removeDirectoryPath = (index) => {
    setDirectoryPaths(directoryPaths.filter((_, i) => i !== index))
  }

  const updateDirectoryPath = (index, value) => {
    const newPaths = [...directoryPaths]
    newPaths[index] = value
    setDirectoryPaths(newPaths)
  }

  const handleImport = async () => {
    setImporting(true)
    setImportResults(null)

    try {
      const validFilePaths = filePaths.filter(path => path.trim() !== '')
      const validDirectoryPaths = directoryPaths.filter(path => path.trim() !== '')

      if (validFilePaths.length === 0 && validDirectoryPaths.length === 0) {
        alert('Please specify at least one file or directory to import')
        setImporting(false)
        return
      }

      const response = await fetch('/api/notes/import', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          file_paths: validFilePaths,
          directory_paths: validDirectoryPaths,
          process_with_llm: processWithLLM,
          recursive: recursive
        })
      })

      if (response.ok) {
        const results = await response.json()
        setImportResults(results)
        
        if (onImportComplete) {
          onImportComplete(results)
        }
      } else {
        const error = await response.json()
        alert(`Import failed: ${error.error}`)
      }
    } catch (error) {
      console.error('Import error:', error)
      alert('Import failed: Network error')
    } finally {
      setImporting(false)
    }
  }

  const resetDialog = () => {
    setFilePaths([''])
    setDirectoryPaths([''])
    setProcessWithLLM(true)
    setRecursive(true)
    setImportResults(null)
  }

  const closeDialog = () => {
    setOpen(false)
    setTimeout(resetDialog, 300) // Reset after dialog closes
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button>
          <Upload className="h-4 w-4 mr-2" />
          Import Notes
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Upload className="h-5 w-5" />
            Import Notes and Files
          </DialogTitle>
          <DialogDescription>
            Import individual files or entire directories. Supports .md and .json files.
          </DialogDescription>
        </DialogHeader>

        {!importResults ? (
          <div className="space-y-6">
            {/* File Paths Section */}
            <div className="space-y-3">
              <Label className="text-base font-medium flex items-center gap-2">
                <FileText className="h-4 w-4" />
                Individual Files
              </Label>
              <p className="text-sm text-muted-foreground">
                Specify full paths to individual .md or .json files
              </p>
              
              {filePaths.map((path, index) => (
                <div key={index} className="flex gap-2">
                  <Input
                    placeholder="/path/to/your/note.md"
                    value={path}
                    onChange={(e) => updateFilePath(index, e.target.value)}
                    className="flex-1"
                  />
                  {filePaths.length > 1 && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => removeFilePath(index)}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              ))}
              
              <Button
                variant="outline"
                size="sm"
                onClick={addFilePath}
                className="w-full"
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Another File
              </Button>
            </div>

            {/* Directory Paths Section */}
            <div className="space-y-3">
              <Label className="text-base font-medium flex items-center gap-2">
                <FolderOpen className="h-4 w-4" />
                Directories
              </Label>
              <p className="text-sm text-muted-foreground">
                Specify directories containing .md or .json files
              </p>
              
              {directoryPaths.map((path, index) => (
                <div key={index} className="flex gap-2">
                  <Input
                    placeholder="/path/to/your/notes/directory"
                    value={path}
                    onChange={(e) => updateDirectoryPath(index, e.target.value)}
                    className="flex-1"
                  />
                  {directoryPaths.length > 1 && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => removeDirectoryPath(index)}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              ))}
              
              <Button
                variant="outline"
                size="sm"
                onClick={addDirectoryPath}
                className="w-full"
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Another Directory
              </Button>
            </div>

            {/* Options */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-base">Import Options</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="recursive"
                    checked={recursive}
                    onCheckedChange={setRecursive}
                  />
                  <Label htmlFor="recursive" className="text-sm">
                    Search subdirectories recursively
                  </Label>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="llm-processing"
                    checked={processWithLLM}
                    onCheckedChange={setProcessWithLLM}
                  />
                  <Label htmlFor="llm-processing" className="text-sm flex items-center gap-2">
                    <Sparkles className="h-4 w-4" />
                    Process with LLM (extract topics, generate tags, create summaries)
                  </Label>
                </div>
              </CardContent>
            </Card>

            {/* Action Buttons */}
            <div className="flex justify-end gap-2">
              <Button variant="outline" onClick={closeDialog}>
                Cancel
              </Button>
              <Button onClick={handleImport} disabled={importing}>
                {importing ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Importing...
                  </>
                ) : (
                  <>
                    <Upload className="h-4 w-4 mr-2" />
                    Import
                  </>
                )}
              </Button>
            </div>
          </div>
        ) : (
          /* Import Results */
          <div className="space-y-4">
            <div className="flex items-center gap-2 text-green-600">
              <CheckCircle className="h-5 w-5" />
              <span className="font-medium">Import Completed</span>
            </div>

            <Card>
              <CardHeader>
                <CardTitle className="text-base">Import Summary</CardTitle>
                <CardDescription>
                  Successfully imported {importResults.count} {importResults.count === 1 ? 'note' : 'notes'}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3 max-h-60 overflow-y-auto">
                  {importResults.imported_notes.map((note) => (
                    <div key={note.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex-1">
                        <h4 className="font-medium text-sm">{note.title}</h4>
                        <div className="flex items-center gap-2 mt-1">
                          <Badge variant="outline" className="text-xs">
                            {note.file_type.toUpperCase()}
                          </Badge>
                          {note.tags && note.tags.length > 0 && (
                            <span className="text-xs text-muted-foreground">
                              {note.tags.length} tags
                            </span>
                          )}
                          {note.topics && note.topics.length > 0 && (
                            <span className="text-xs text-muted-foreground">
                              {note.topics.length} topics
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <div className="flex justify-end gap-2">
              <Button variant="outline" onClick={resetDialog}>
                Import More
              </Button>
              <Button onClick={closeDialog}>
                Done
              </Button>
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  )
}

export default ImportDialog

