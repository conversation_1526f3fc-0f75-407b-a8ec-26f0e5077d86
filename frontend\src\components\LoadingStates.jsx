/**
 * Loading state components for different UI scenarios
 */
import React from 'react';
import { Loader2 } from 'lucide-react';

/**
 * Generic spinner component
 */
export function Spinner({ size = 'md', className = '' }) {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8',
    xl: 'w-12 h-12',
  };

  return (
    <Loader2 
      className={`animate-spin ${sizeClasses[size]} ${className}`} 
    />
  );
}

/**
 * Full page loading screen
 */
export function PageLoader({ message = 'Loading...' }) {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="text-center">
        <Spinner size="xl" className="text-blue-600 mx-auto mb-4" />
        <p className="text-gray-600 text-lg">{message}</p>
      </div>
    </div>
  );
}

/**
 * Card/section loading skeleton
 */
export function CardSkeleton({ lines = 3, className = '' }) {
  return (
    <div className={`animate-pulse ${className}`}>
      <div className="h-4 bg-gray-200 rounded w-3/4 mb-3"></div>
      {Array.from({ length: lines }).map((_, i) => (
        <div 
          key={i}
          className={`h-3 bg-gray-200 rounded mb-2 ${
            i === lines - 1 ? 'w-1/2' : 'w-full'
          }`}
        ></div>
      ))}
    </div>
  );
}

/**
 * List loading skeleton
 */
export function ListSkeleton({ items = 5, className = '' }) {
  return (
    <div className={`space-y-4 ${className}`}>
      {Array.from({ length: items }).map((_, i) => (
        <div key={i} className="border border-gray-200 rounded-lg p-4">
          <CardSkeleton lines={2} />
        </div>
      ))}
    </div>
  );
}

/**
 * Table loading skeleton
 */
export function TableSkeleton({ rows = 5, columns = 4, className = '' }) {
  return (
    <div className={`animate-pulse ${className}`}>
      <div className="border border-gray-200 rounded-lg overflow-hidden">
        {/* Header */}
        <div className="bg-gray-50 border-b border-gray-200 p-4">
          <div className="grid gap-4" style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}>
            {Array.from({ length: columns }).map((_, i) => (
              <div key={i} className="h-4 bg-gray-300 rounded"></div>
            ))}
          </div>
        </div>
        
        {/* Rows */}
        {Array.from({ length: rows }).map((_, rowIndex) => (
          <div key={rowIndex} className="border-b border-gray-200 p-4 last:border-b-0">
            <div className="grid gap-4" style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}>
              {Array.from({ length: columns }).map((_, colIndex) => (
                <div 
                  key={colIndex} 
                  className={`h-3 bg-gray-200 rounded ${
                    colIndex === 0 ? 'w-3/4' : 'w-full'
                  }`}
                ></div>
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

/**
 * Button loading state
 */
export function LoadingButton({ 
  loading = false, 
  children, 
  disabled = false,
  className = '',
  ...props 
}) {
  return (
    <button
      disabled={loading || disabled}
      className={`relative ${className} ${
        loading ? 'cursor-not-allowed opacity-75' : ''
      }`}
      {...props}
    >
      {loading && (
        <div className="absolute inset-0 flex items-center justify-center">
          <Spinner size="sm" className="text-current" />
        </div>
      )}
      <span className={loading ? 'invisible' : 'visible'}>
        {children}
      </span>
    </button>
  );
}

/**
 * Inline loading indicator
 */
export function InlineLoader({ message = 'Loading...', className = '' }) {
  return (
    <div className={`flex items-center gap-2 text-gray-600 ${className}`}>
      <Spinner size="sm" />
      <span className="text-sm">{message}</span>
    </div>
  );
}

/**
 * Content placeholder for empty states
 */
export function EmptyState({ 
  icon: Icon, 
  title, 
  description, 
  action,
  className = '' 
}) {
  return (
    <div className={`text-center py-12 ${className}`}>
      {Icon && (
        <div className="flex justify-center mb-4">
          <Icon className="w-12 h-12 text-gray-400" />
        </div>
      )}
      
      {title && (
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          {title}
        </h3>
      )}
      
      {description && (
        <p className="text-gray-600 mb-6 max-w-sm mx-auto">
          {description}
        </p>
      )}
      
      {action}
    </div>
  );
}

/**
 * Loading overlay for existing content
 */
export function LoadingOverlay({ loading = false, children, message = 'Loading...' }) {
  return (
    <div className="relative">
      {children}
      {loading && (
        <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center z-10">
          <div className="text-center">
            <Spinner size="lg" className="text-blue-600 mx-auto mb-2" />
            <p className="text-gray-600">{message}</p>
          </div>
        </div>
      )}
    </div>
  );
}

/**
 * Progressive loading component
 */
export function ProgressiveLoader({ 
  steps = [], 
  currentStep = 0, 
  className = '' 
}) {
  return (
    <div className={`space-y-4 ${className}`}>
      <div className="text-center mb-6">
        <Spinner size="lg" className="text-blue-600 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          {steps[currentStep]?.title || 'Processing...'}
        </h3>
        <p className="text-gray-600">
          {steps[currentStep]?.description || 'Please wait...'}
        </p>
      </div>
      
      {steps.length > 1 && (
        <div className="max-w-md mx-auto">
          <div className="flex justify-between text-xs text-gray-500 mb-2">
            <span>Step {currentStep + 1} of {steps.length}</span>
            <span>{Math.round(((currentStep + 1) / steps.length) * 100)}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${((currentStep + 1) / steps.length) * 100}%` }}
            ></div>
          </div>
        </div>
      )}
    </div>
  );
}

export default {
  Spinner,
  PageLoader,
  CardSkeleton,
  ListSkeleton,
  TableSkeleton,
  LoadingButton,
  InlineLoader,
  EmptyState,
  LoadingOverlay,
  ProgressiveLoader,
};
