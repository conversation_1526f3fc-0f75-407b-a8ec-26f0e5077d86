[project]
name = "note-manager"
version = "0.1.0"
description = "Local knowledge management system with note management and LLM integration"
authors = [
    { name="Bloga", email="" }
]
requires-python = ">=3.8"

[project.dependencies]
flask = "==2.3.3"
flask-sqlalchemy = "==3.0.5"
flask-cors = "==4.0.0"
litellm = "==1.17.9"
python-frontmatter = "==1.0.0"

[project.optional-dependencies]
test = ["pytest", "pytest-cov", "pytest-flask"]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.uv]
scripts = {
    "dev" = "python -m flask run",
    "test" = "pytest",
    "lint" = "ruff .",
    "format" = "black ."
}

[tool.uv.dependencies]
python = "^3.11"

[tool.uv.install]
no-cache = true

[tool.uv.lock]
version = 1
