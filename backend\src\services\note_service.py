"""
Note service implementation
"""
import json
from typing import List, Optional, Dict, Any
from sqlalchemy import or_

from src.models import db, Note, Tag, NoteLink
from src.services.interfaces import INoteService, ILLMService, ITagService
from src.utils.logging_config import get_logger

logger = get_logger('airchivist.note_service')


class NoteService(INoteService):
    """
    Service for managing notes
    """
    
    def __init__(self, llm_service: ILLMService = None, tag_service: ITagService = None):
        self.llm_service = llm_service
        self.tag_service = tag_service
    
    def get_notes(self, page: int = 1, per_page: int = 20, search: str = None, 
                  tag_filter: str = None) -> Dict[str, Any]:
        """
        Get notes with pagination and filtering
        
        Args:
            page: Page number
            per_page: Items per page
            search: Search term
            tag_filter: Tag filter
            
        Returns:
            Dictionary with notes, pagination info, and metadata
        """
        try:
            query = Note.query
            
            # Apply search filter
            if search:
                search_term = f"%{search}%"
                query = query.filter(
                    or_(
                        Note.title.ilike(search_term),
                        Note.content.ilike(search_term),
                        Note.summary.ilike(search_term)
                    )
                )
            
            # Apply tag filter
            if tag_filter:
                query = query.join(Note.tags).filter(Tag.name.ilike(f"%{tag_filter}%"))
            
            # Get paginated results
            paginated = query.paginate(
                page=page, 
                per_page=per_page, 
                error_out=False
            )
            
            result = {
                'notes': [note.to_dict() for note in paginated.items],
                'pagination': {
                    'page': page,
                    'per_page': per_page,
                    'total': paginated.total,
                    'pages': paginated.pages,
                    'has_prev': paginated.has_prev,
                    'has_next': paginated.has_next,
                    'prev_num': paginated.prev_num,
                    'next_num': paginated.next_num
                },
                'filters': {
                    'search': search,
                    'tag': tag_filter
                }
            }
            
            logger.debug(f"Retrieved {len(paginated.items)} notes (page {page}/{paginated.pages})")
            return result
            
        except Exception as e:
            logger.error(f"Error retrieving notes: {e}", exc_info=True)
            return {
                'notes': [],
                'pagination': {
                    'page': page,
                    'per_page': per_page,
                    'total': 0,
                    'pages': 0,
                    'has_prev': False,
                    'has_next': False,
                    'prev_num': None,
                    'next_num': None
                },
                'filters': {
                    'search': search,
                    'tag': tag_filter
                }
            }
    
    def get_note(self, note_id: int) -> Optional[Note]:
        """
        Get a specific note
        
        Args:
            note_id: Note ID
            
        Returns:
            Note if found, None otherwise
        """
        try:
            note = Note.query.get(note_id)
            if note:
                logger.debug(f"Retrieved note {note_id}: {note.title}")
            else:
                logger.warning(f"Note {note_id} not found")
            return note
        except Exception as e:
            logger.error(f"Error retrieving note {note_id}: {e}", exc_info=True)
            return None
    
    def create_note(self, title: str, content: str, tags: List[str] = None) -> Note:
        """
        Create a new note
        
        Args:
            title: Note title
            content: Note content
            tags: List of tag names
            
        Returns:
            Created note
        """
        try:
            # Create note
            note = Note(title=title, content=content)
            db.session.add(note)
            
            # Add tags if provided
            if tags and self.tag_service:
                for tag_name in tags:
                    tag = self.tag_service.get_or_create_tag(tag_name)
                    note.tags.append(tag)
            
            # Process with LLM if available
            if self.llm_service:
                try:
                    # Generate summary
                    summary = self.llm_service.summarize_content(content)
                    note.summary = summary
                    
                    # Extract topics
                    topics = self.llm_service.extract_topics(content)
                    note.topics = json.dumps(topics)
                    
                    # Generate additional tags
                    existing_tag_names = [tag.name for tag in note.tags]
                    new_tags = self.llm_service.generate_tags(content, existing_tag_names)
                    
                    for tag_name in new_tags:
                        if tag_name not in existing_tag_names:
                            tag = self.tag_service.get_or_create_tag(tag_name)
                            note.tags.append(tag)
                            
                except Exception as e:
                    logger.warning(f"LLM processing failed for new note: {e}")
            
            db.session.commit()
            logger.info(f"Created note: {title}")
            return note
            
        except Exception as e:
            db.session.rollback()
            logger.error(f"Error creating note '{title}': {e}", exc_info=True)
            raise
    
    def update_note(self, note_id: int, title: str = None, content: str = None, 
                    tags: List[str] = None) -> Optional[Note]:
        """
        Update a note
        
        Args:
            note_id: Note ID
            title: New title (optional)
            content: New content (optional)
            tags: New tags list (optional)
            
        Returns:
            Updated note or None if not found
        """
        try:
            note = Note.query.get(note_id)
            if not note:
                logger.warning(f"Note {note_id} not found for update")
                return None
            
            # Update fields
            if title is not None:
                note.title = title
            
            if content is not None:
                note.content = content
                
                # Re-process with LLM if content changed
                if self.llm_service:
                    try:
                        summary = self.llm_service.summarize_content(content)
                        note.summary = summary
                        
                        topics = self.llm_service.extract_topics(content)
                        note.topics = json.dumps(topics)
                    except Exception as e:
                        logger.warning(f"LLM processing failed for updated note {note_id}: {e}")
            
            # Update tags if provided
            if tags is not None and self.tag_service:
                # Clear existing tags
                note.tags.clear()
                
                # Add new tags
                for tag_name in tags:
                    tag = self.tag_service.get_or_create_tag(tag_name)
                    note.tags.append(tag)
            
            db.session.commit()
            logger.info(f"Updated note {note_id}: {note.title}")
            return note
            
        except Exception as e:
            db.session.rollback()
            logger.error(f"Error updating note {note_id}: {e}", exc_info=True)
            raise
    
    def delete_note(self, note_id: int) -> bool:
        """
        Delete a note
        
        Args:
            note_id: Note ID
            
        Returns:
            True if deleted, False if not found
        """
        try:
            note = Note.query.get(note_id)
            if not note:
                logger.warning(f"Note {note_id} not found for deletion")
                return False
            
            # Delete associated links
            NoteLink.query.filter(
                or_(NoteLink.from_note_id == note_id, NoteLink.to_note_id == note_id)
            ).delete()
            
            db.session.delete(note)
            db.session.commit()
            
            logger.info(f"Deleted note {note_id}: {note.title}")
            return True
            
        except Exception as e:
            db.session.rollback()
            logger.error(f"Error deleting note {note_id}: {e}", exc_info=True)
            raise
    
    def get_graph_data(self) -> Dict[str, Any]:
        """
        Get graph data for visualization
        
        Returns:
            Dictionary with nodes and edges for graph visualization
        """
        try:
            # Get all notes
            notes = Note.query.all()
            
            # Create nodes
            nodes = []
            for note in notes:
                nodes.append({
                    'id': note.id,
                    'title': note.title,
                    'summary': note.summary or note.content[:100] + '...' if len(note.content) > 100 else note.content,
                    'tags': [tag.name for tag in note.tags],
                    'created_at': note.created_at.isoformat() if note.created_at else None
                })
            
            # Get all links
            links = NoteLink.query.all()
            
            # Create edges
            edges = []
            for link in links:
                edges.append({
                    'id': link.id,
                    'from': link.from_note_id,
                    'to': link.to_note_id,
                    'type': link.link_type
                })
            
            result = {
                'nodes': nodes,
                'edges': edges,
                'stats': {
                    'total_notes': len(notes),
                    'total_links': len(links),
                    'total_tags': len(set(tag.name for note in notes for tag in note.tags))
                }
            }
            
            logger.debug(f"Generated graph data: {len(nodes)} nodes, {len(edges)} edges")
            return result
            
        except Exception as e:
            logger.error(f"Error generating graph data: {e}", exc_info=True)
            return {
                'nodes': [],
                'edges': [],
                'stats': {
                    'total_notes': 0,
                    'total_links': 0,
                    'total_tags': 0
                }
            }
