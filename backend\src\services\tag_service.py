"""
Tag service implementation
"""
from typing import List, Optional
from sqlalchemy.exc import IntegrityError

from src.models import db, Tag
from src.services.interfaces import ITagService
from src.utils.logging_config import get_logger

logger = get_logger('airchivist.tag_service')


class TagService(ITagService):
    """
    Service for managing tags
    """
    
    def get_all_tags(self) -> List[Tag]:
        """
        Get all tags
        
        Returns:
            List of all tags
        """
        try:
            tags = Tag.query.all()
            logger.debug(f"Retrieved {len(tags)} tags")
            return tags
        except Exception as e:
            logger.error(f"Error retrieving tags: {e}", exc_info=True)
            return []
    
    def create_tag(self, name: str, color: str = None) -> Tag:
        """
        Create a new tag
        
        Args:
            name: Tag name
            color: Optional tag color
            
        Returns:
            Created tag
            
        Raises:
            ValueError: If tag name already exists
        """
        try:
            # Normalize tag name
            normalized_name = name.lower().strip()
            
            if not normalized_name:
                raise ValueError("Tag name cannot be empty")
            
            # Check if tag already exists
            existing_tag = Tag.query.filter_by(name=normalized_name).first()
            if existing_tag:
                raise ValueError(f"Tag '{normalized_name}' already exists")
            
            # Create new tag
            tag = Tag(name=normalized_name, color=color)
            db.session.add(tag)
            db.session.commit()
            
            logger.info(f"Created tag: {normalized_name}")
            return tag
            
        except IntegrityError as e:
            db.session.rollback()
            logger.error(f"Integrity error creating tag '{name}': {e}")
            raise ValueError(f"Tag '{name}' already exists")
        except Exception as e:
            db.session.rollback()
            logger.error(f"Error creating tag '{name}': {e}", exc_info=True)
            raise
    
    def update_tag(self, tag_id: int, name: str = None, color: str = None) -> Optional[Tag]:
        """
        Update a tag
        
        Args:
            tag_id: Tag ID
            name: New tag name (optional)
            color: New tag color (optional)
            
        Returns:
            Updated tag or None if not found
            
        Raises:
            ValueError: If new name already exists
        """
        try:
            tag = Tag.query.get(tag_id)
            if not tag:
                logger.warning(f"Tag with ID {tag_id} not found")
                return None
            
            # Update name if provided
            if name is not None:
                normalized_name = name.lower().strip()
                if not normalized_name:
                    raise ValueError("Tag name cannot be empty")
                
                # Check if new name conflicts with existing tag
                if normalized_name != tag.name:
                    existing_tag = Tag.query.filter_by(name=normalized_name).first()
                    if existing_tag:
                        raise ValueError(f"Tag '{normalized_name}' already exists")
                    tag.name = normalized_name
            
            # Update color if provided
            if color is not None:
                tag.color = color
            
            db.session.commit()
            logger.info(f"Updated tag {tag_id}: {tag.name}")
            return tag
            
        except IntegrityError as e:
            db.session.rollback()
            logger.error(f"Integrity error updating tag {tag_id}: {e}")
            raise ValueError(f"Tag name already exists")
        except Exception as e:
            db.session.rollback()
            logger.error(f"Error updating tag {tag_id}: {e}", exc_info=True)
            raise
    
    def delete_tag(self, tag_id: int) -> bool:
        """
        Delete a tag
        
        Args:
            tag_id: Tag ID
            
        Returns:
            True if deleted, False if not found
        """
        try:
            tag = Tag.query.get(tag_id)
            if not tag:
                logger.warning(f"Tag with ID {tag_id} not found")
                return False
            
            # Remove tag from all notes first
            for note in tag.notes:
                note.tags.remove(tag)
            
            db.session.delete(tag)
            db.session.commit()
            
            logger.info(f"Deleted tag {tag_id}: {tag.name}")
            return True
            
        except Exception as e:
            db.session.rollback()
            logger.error(f"Error deleting tag {tag_id}: {e}", exc_info=True)
            raise
    
    def get_or_create_tag(self, name: str, color: str = None) -> Tag:
        """
        Get existing tag or create new one
        
        Args:
            name: Tag name
            color: Optional tag color (only used if creating new tag)
            
        Returns:
            Existing or newly created tag
        """
        try:
            # Normalize tag name
            normalized_name = name.lower().strip()
            
            if not normalized_name:
                raise ValueError("Tag name cannot be empty")
            
            # Try to get existing tag
            tag = Tag.query.filter_by(name=normalized_name).first()
            if tag:
                logger.debug(f"Found existing tag: {normalized_name}")
                return tag
            
            # Create new tag
            tag = Tag(name=normalized_name, color=color)
            db.session.add(tag)
            db.session.commit()
            
            logger.info(f"Created new tag: {normalized_name}")
            return tag
            
        except Exception as e:
            db.session.rollback()
            logger.error(f"Error getting or creating tag '{name}': {e}", exc_info=True)
            raise
    
    def get_tag_by_name(self, name: str) -> Optional[Tag]:
        """
        Get tag by name
        
        Args:
            name: Tag name
            
        Returns:
            Tag if found, None otherwise
        """
        try:
            normalized_name = name.lower().strip()
            tag = Tag.query.filter_by(name=normalized_name).first()
            return tag
        except Exception as e:
            logger.error(f"Error getting tag by name '{name}': {e}", exc_info=True)
            return None
    
    def get_popular_tags(self, limit: int = 10) -> List[tuple]:
        """
        Get most popular tags by usage count
        
        Args:
            limit: Maximum number of tags to return
            
        Returns:
            List of tuples (tag, count)
        """
        try:
            # This would require a more complex query to count note associations
            # For now, return all tags sorted by name
            tags = Tag.query.limit(limit).all()
            return [(tag, len(tag.notes)) for tag in tags]
        except Exception as e:
            logger.error(f"Error getting popular tags: {e}", exc_info=True)
            return []
