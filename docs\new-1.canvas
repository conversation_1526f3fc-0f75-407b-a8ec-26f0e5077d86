{"nodes": [{"type": "text", "id": "49777ccd-e2da-4989-8f60-ba76e0f2fbbd", "x": -745.8000030517578, "y": 593.8666625976563, "width": 126, "height": 108, "color": "6", "text": "## <center>User</center>  \n <center>Note User</center> "}, {"type": "nested-canvas", "id": "be52fdcf-a2fe-45c2-a344-7145ecae1c71", "x": -469.8000030517578, "y": 557.8666625976563, "width": 420, "height": 180, "canvas": {"nodes": [{"type": "text", "id": "ec698086-e51e-4cc8-bd7d-114ad69b031b", "x": 288, "y": 44.4, "width": 226, "height": 108, "color": "6", "text": "\n---\nfiles: [\"frontend/src/main.jsx\",\"frontend/src/App.jsx\"]\n---\n\n \n ## <center>Main Application</center>  \n  <center>React</center> "}, {"type": "text", "id": "0072f69e-b479-4123-9799-95b08dfe7f56", "x": 814, "y": 328, "width": 149, "height": 108, "color": "6", "text": "\n---\nfiles: [\"frontend/src/components/NoteList.jsx\"]\n---\n\n \n ## <center>Note List</center>  \n  <center>React</center> "}, {"type": "text", "id": "4f41b2d0-957c-41e2-b5c0-c18b206348bf", "x": 814, "y": 486, "width": 181, "height": 108, "color": "6", "text": "\n---\nfiles: [\"frontend/src/components/NoteViewer.jsx\"]\n---\n\n \n ## <center>Note Viewer</center>  \n  <center>React</center> "}, {"type": "text", "id": "f3a7ec76-8143-4e51-aabf-91cdd353201f", "x": 814, "y": 12, "width": 173, "height": 108, "color": "6", "text": "\n---\nfiles: [\"frontend/src/components/GraphView.jsx\"]\n---\n\n \n ## <center>Graph View</center>  \n  <center>React</center> "}, {"type": "text", "id": "fa1f5adb-c6b7-4dc5-b3ac-29358fa97901", "x": 814, "y": 170, "width": 197, "height": 108, "color": "6", "text": "\n---\nfiles: [\"frontend/src/components/ImportDialog.jsx\"]\n---\n\n \n ## <center>Import Dialog</center>  \n  <center>React</center> "}, {"type": "text", "id": "28c5ecde-2737-465c-b5e4-acb01f1210f7", "x": 12, "y": 44.4, "width": 126, "height": 108, "color": "1", "text": "## <center>User</center>  \n <center>Note User</center> "}], "edges": [{"id": "cd9a1028-6827-4b00-b74f-def2176f1c90", "fromNode": "ec698086-e51e-4cc8-bd7d-114ad69b031b", "fromSide": "right", "toNode": "0072f69e-b479-4123-9799-95b08dfe7f56", "toSide": "left", "toEnd": "arrow", "label": "Displays notes"}, {"id": "4d4bf5b6-4386-4e34-a9b3-3264c8ac5611", "fromNode": "ec698086-e51e-4cc8-bd7d-114ad69b031b", "fromSide": "right", "toNode": "4f41b2d0-957c-41e2-b5c0-c18b206348bf", "toSide": "left", "toEnd": "arrow", "label": "Manages note view"}, {"id": "e2b58f6a-3183-47e8-abac-5742fde6eda7", "fromNode": "ec698086-e51e-4cc8-bd7d-114ad69b031b", "fromSide": "right", "toNode": "f3a7ec76-8143-4e51-aabf-91cdd353201f", "toSide": "left", "toEnd": "arrow", "label": "Shows graph"}, {"id": "498d1333-a0b0-4504-99d2-f3474a274f92", "fromNode": "ec698086-e51e-4cc8-bd7d-114ad69b031b", "fromSide": "right", "toNode": "fa1f5adb-c6b7-4dc5-b3ac-29358fa97901", "toSide": "left", "toEnd": "arrow", "label": "Handles imports"}, {"id": "6a813ef6-107c-4907-bb02-b8d40baf7e08", "fromNode": "28c5ecde-2737-465c-b5e4-acb01f1210f7", "fromSide": "right", "toNode": "ec698086-e51e-4cc8-bd7d-114ad69b031b", "toSide": "left", "toEnd": "arrow", "label": "Uses application"}]}, "title": "Frontend App | React, Vite"}, {"type": "nested-canvas", "id": "ec5346d5-b77d-4cbe-a9bd-d633811fc948", "x": 100.19999694824219, "y": 557.8666625976563, "width": 420, "height": 180, "canvas": {"nodes": [{"type": "text", "id": "f1344211-8c4a-4e90-ad2c-7bef38bcade3", "x": 388, "y": 44.4, "width": 166, "height": 108, "color": "6", "text": "\n---\nfiles: [\"backend/main.py\",\"backend/src/routes/notes.py\",\"backend/src/routes/user.py\"]\n---\n\n \n ## <center>API Routes</center>  \n  <center>Python, Flask</center> "}, {"type": "text", "id": "26167716-179a-433e-9448-27c21a0381b1", "x": 854, "y": 328, "width": 177, "height": 108, "color": "6", "text": "\n---\nfiles: [\"backend/src/services/llm_service.py\"]\n---\n\n \n ## <center>LLM Service</center>  \n  <center>Python</center> "}, {"type": "text", "id": "fe7f3a87-17a8-44e7-b974-60c878d832f6", "x": 854, "y": 486, "width": 170, "height": 108, "color": "6", "text": "\n---\nfiles: [\"backend/src/services/file_service.py\"]\n---\n\n \n ## <center>File Service</center>  \n  <center>Python</center> "}, {"type": "text", "id": "89e69659-6110-4aaa-8d2b-94775c9062c0", "x": 854, "y": 12, "width": 176, "height": 108, "color": "6", "text": "\n---\nfiles: [\"backend/src/models/note.py\"]\n---\n\n \n ## <center>Note Model</center>  \n  <center>Python</center> "}, {"type": "text", "id": "b57268be-250f-4954-87eb-5fb55c878d23", "x": 854, "y": 170, "width": 172, "height": 108, "color": "6", "text": "\n---\nfiles: [\"backend/src/models/user.py\"]\n---\n\n \n ## <center>User Model</center>  \n  <center>Python</center> "}, {"type": "text", "id": "58ec522c-cd6b-4a2c-9bec-55850099b0eb", "x": 12, "y": 44.4, "width": 226, "height": 108, "color": "1", "text": "\n---\nfiles: [\"frontend/src/main.jsx\",\"frontend/src/App.jsx\"]\n---\n\n \n ## <center>Main Application</center>  \n  <center>React</center> "}], "edges": [{"id": "81572865-058a-4580-99e4-95e07bc25df8", "fromNode": "f1344211-8c4a-4e90-ad2c-7bef38bcade3", "fromSide": "right", "toNode": "26167716-179a-433e-9448-27c21a0381b1", "toSide": "left", "toEnd": "arrow", "label": "Invokes LLM"}, {"id": "9462ab43-061d-4b08-a119-a98834fcc6a2", "fromNode": "f1344211-8c4a-4e90-ad2c-7bef38bcade3", "fromSide": "right", "toNode": "fe7f3a87-17a8-44e7-b974-60c878d832f6", "toSide": "left", "toEnd": "arrow", "label": "Manages files"}, {"id": "1a98fbc0-88b2-4b77-8573-b8b05a82667f", "fromNode": "f1344211-8c4a-4e90-ad2c-7bef38bcade3", "fromSide": "right", "toNode": "89e69659-6110-4aaa-8d2b-94775c9062c0", "toSide": "left", "toEnd": "arrow", "label": "Uses note model"}, {"id": "2bf853ce-f259-4eaf-b200-a8cdc1626022", "fromNode": "f1344211-8c4a-4e90-ad2c-7bef38bcade3", "fromSide": "right", "toNode": "b57268be-250f-4954-87eb-5fb55c878d23", "toSide": "left", "toEnd": "arrow", "label": "Uses user model"}, {"id": "8db91ec3-b3f2-4da2-846d-e7e3bb77b308", "fromNode": "58ec522c-cd6b-4a2c-9bec-55850099b0eb", "fromSide": "right", "toNode": "f1344211-8c4a-4e90-ad2c-7bef38bcade3", "toSide": "left", "toEnd": "arrow", "label": "Makes API calls"}]}, "title": "Backend API | Python, Flask"}, {"type": "nested-canvas", "id": "850a11ee-033c-469c-b6ed-8ed53e89b871", "x": 670.1999969482422, "y": 557.8666625976563, "width": 420, "height": 180, "canvas": {"nodes": [{"type": "text", "id": "dcdd4a7c-e0c1-45fb-8b63-0a1e03932a5a", "x": 339, "y": 12, "width": 189, "height": 108, "color": "6", "text": "\n---\nfiles: []\n---\n\n \n ## <center>LLM Provider</center>  \n  <center>OpenAI/Local LLM</center> "}, {"type": "text", "id": "32c23f61-ea25-4235-8c66-feeee83d8b18", "x": 332, "y": 270, "width": 217, "height": 108, "color": "6", "text": "\n---\nfiles: [\"data/notes/\",\"data/conversation.json\"]\n---\n\n \n ## <center>Local Data Store</center>  \n  <center>Filesystem</center> "}, {"type": "text", "id": "022ef6e4-9404-45f8-abc2-bf6aef99fec9", "x": 12, "y": 12, "width": 177, "height": 108, "color": "1", "text": "\n---\nfiles: [\"backend/src/services/llm_service.py\"]\n---\n\n \n ## <center>LLM Service</center>  \n  <center>Python</center> "}, {"type": "text", "id": "eaeb3119-7753-4419-b213-eff0cc7df277", "x": 12, "y": 270, "width": 170, "height": 108, "color": "1", "text": "\n---\nfiles: [\"backend/src/services/file_service.py\"]\n---\n\n \n ## <center>File Service</center>  \n  <center>Python</center> "}], "edges": [{"id": "bfed64f4-7f74-498c-8dfd-ec9b5690b142", "fromNode": "022ef6e4-9404-45f8-abc2-bf6aef99fec9", "fromSide": "right", "toNode": "dcdd4a7c-e0c1-45fb-8b63-0a1e03932a5a", "toSide": "left", "toEnd": "arrow", "label": "Queries LLM"}, {"id": "ab58f70e-1fc3-4f3d-8120-01685617ae1e", "fromNode": "eaeb3119-7753-4419-b213-eff0cc7df277", "fromSide": "right", "toNode": "32c23f61-ea25-4235-8c66-feeee83d8b18", "toSide": "left", "toEnd": "arrow", "label": "Reads/writes files"}]}, "title": "External Systems | Various"}], "edges": [{"id": "6a813ef6-107c-4907-bb02-b8d40baf7e08", "fromNode": "49777ccd-e2da-4989-8f60-ba76e0f2fbbd", "fromSide": "right", "toNode": "be52fdcf-a2fe-45c2-a344-7145ecae1c71", "toSide": "left", "toEnd": "arrow", "label": "Uses application"}, {"id": "8db91ec3-b3f2-4da2-846d-e7e3bb77b308", "fromNode": "be52fdcf-a2fe-45c2-a344-7145ecae1c71", "fromSide": "right", "toNode": "ec5346d5-b77d-4cbe-a9bd-d633811fc948", "toSide": "left", "toEnd": "arrow", "label": "Makes API calls"}, {"id": "bfed64f4-7f74-498c-8dfd-ec9b5690b142", "fromNode": "ec5346d5-b77d-4cbe-a9bd-d633811fc948", "fromSide": "right", "toNode": "850a11ee-033c-469c-b6ed-8ed53e89b871", "toSide": "left", "toEnd": "arrow", "label": "Queries LLM"}]}