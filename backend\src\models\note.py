import json
from datetime import datetime

from . import db


class Note(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(255), nullable=False)
    content = db.Column(db.Text, nullable=False)
    file_path = db.Column(db.String(500), nullable=False, unique=True)
    file_type = db.Column(db.String(10), nullable=False)  # 'md' or 'json'
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # LLM-generated fields
    summary = db.Column(db.Text)
    topics = db.Column(db.Text)  # JSON string of extracted topics
    
    # Relationships
    tags = db.relationship('Tag', secondary='note_tags', back_populates='notes')
    links_from = db.relationship('NoteLink', foreign_keys='NoteLink.from_note_id', back_populates='from_note')
    links_to = db.relationship('NoteLink', foreign_keys='NoteLink.to_note_id', back_populates='to_note')

    def __repr__(self):
        return f'<Note {self.title}>'

    def to_dict(self):
        return {
            'id': self.id,
            'title': self.title,
            'content': self.content,
            'file_path': self.file_path,
            'file_type': self.file_type,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'summary': self.summary,
            'topics': json.loads(self.topics) if self.topics else [],
            'tags': [tag.to_dict() for tag in self.tags],
            'links_from': [link.to_dict() for link in self.links_from],
            'links_to': [link.to_dict() for link in self.links_to]
        }

class Tag(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, unique=True)
    color = db.Column(db.String(7), default='#007acc')  # Hex color code
    
    # Relationships
    notes = db.relationship('Note', secondary='note_tags', back_populates='tags')

    def __repr__(self):
        return f'<Tag {self.name}>'

    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'color': self.color
        }

class NoteLink(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    from_note_id = db.Column(db.Integer, db.ForeignKey('note.id'), nullable=False)
    to_note_id = db.Column(db.Integer, db.ForeignKey('note.id'), nullable=False)
    link_type = db.Column(db.String(50), default='reference')  # 'reference', 'topic_similarity', etc.
    
    # Relationships
    from_note = db.relationship('Note', foreign_keys=[from_note_id], back_populates='links_from')
    to_note = db.relationship('Note', foreign_keys=[to_note_id], back_populates='links_to')

    def __repr__(self):
        return f'<NoteLink {self.from_note_id} -> {self.to_note_id}>'

    def to_dict(self):
        return {
            'id': self.id,
            'from_note_id': self.from_note_id,
            'to_note_id': self.to_note_id,
            'link_type': self.link_type
        }

# Association table for many-to-many relationship between notes and tags
note_tags = db.Table('note_tags',
    db.Column('note_id', db.Integer, db.ForeignKey('note.id'), primary_key=True),
    db.Column('tag_id', db.Integer, db.ForeignKey('tag.id'), primary_key=True)
)

class ArchivedFile(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    original_path = db.Column(db.String(500), nullable=False)
    content = db.Column(db.Text, nullable=False)
    file_hash = db.Column(db.String(64), nullable=False)  # SHA-256 hash
    archived_at = db.Column(db.DateTime, default=datetime.utcnow)
    file_size = db.Column(db.Integer)

    def __repr__(self):
        return f'<ArchivedFile {self.original_path}>'

    def to_dict(self):
        return {
            'id': self.id,
            'original_path': self.original_path,
            'file_hash': self.file_hash,
            'archived_at': self.archived_at.isoformat() if self.archived_at else None,
            'file_size': self.file_size
        }

