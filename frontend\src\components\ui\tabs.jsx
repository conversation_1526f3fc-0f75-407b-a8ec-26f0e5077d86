import React from 'react'

export const Tabs = ({ children, value, onValueChange, className = '' }) => {
  return (
    <div className={`tabs ${className}`} data-value={value}>
      {React.Children.map(children, child =>
        React.cloneElement(child, { value, onValueChange })
      )}
    </div>
  )
}

export const TabsList = ({ children, className = '' }) => {
  return (
    <div className={`tabs-list ${className}`} role="tablist">
      {children}
    </div>
  )
}

export const TabsTrigger = ({ children, value, className = '', ...props }) => {
  return (
    <button
      className={`tabs-trigger ${className}`}
      role="tab"
      data-value={value}
      onClick={() => props.onValueChange?.(value)}
      {...props}
    >
      {children}
    </button>
  )
}

export const TabsContent = ({ children, value, className = '', ...props }) => {
  const isActive = props.value === value
  return (
    <div
      className={`tabs-content ${className} ${isActive ? 'active' : ''}`}
      role="tabpanel"
      style={{ display: isActive ? 'block' : 'none' }}
    >
      {children}
    </div>
  )
}
