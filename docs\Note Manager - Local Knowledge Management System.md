# Note Manager - Local Knowledge Management System

A comprehensive tool for managing large collections of notes and LLM conversations with advanced features including LLM integration, graph visualization, and Obsidian compatibility.

## 🚀 Features

- **📁 Easy Import**: Import individual .md and .json files or entire directories
- **🤖 LLM Integration**: Extract topics, generate tags, and create summaries using LiteLLM
- **🔍 Powerful Search**: Full-text search across notes, content, and tags
- **📊 Graph Visualization**: Interactive network view of note relationships (Obsidian-style)
- **💾 100% Local**: All data stays on your machine for complete privacy
- **🔗 Obsidian Compatible**: Maintains compatibility with Obsidian note structure
- **📦 Archive System**: Separate database to preserve original files
- **🏷️ Smart Tagging**: Automatic and manual tag management
- **📱 Responsive UI**: Modern React interface that works on desktop and mobile

## 🏗️ Architecture

### Backend (Flask + SQLAlchemy)
- RESTful API for note management
- SQLite database for metadata and relationships
- LiteLLM integration for multiple LLM providers
- File import and processing system
- Archive system for original files

### Frontend (React + Vite)
- Modern React 18 with functional components
- Interactive graph visualization
- Responsive design with mobile support
- Real-time search and filtering
- Import dialog with progress tracking

## 📋 Requirements

- Python 3.8+
- Node.js 16+
- 4GB RAM (recommended)
- Modern web browser

## 🛠️ Installation

### Backend Setup

1. **Create and activate virtual environment:**
```bash
python3 -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

2. **Install dependencies:**
```bash
pip install flask flask-sqlalchemy flask-cors litellm python-frontmatter
```

3. **Create directory structure:**
```bash
mkdir -p src/models src/routes src/services src/database notes archive
```

### Frontend Setup

1. **Create React application:**
```bash
npx create-react-app note-manager-frontend
cd note-manager-frontend
```

2. **Install dependencies:**
```bash
npm install lucide-react
```

3. **Configure Vite proxy (vite.config.js):**
```javascript
export default defineConfig({
  server: {
    proxy: {
      '/api': {
        target: 'http://localhost:5000',
        changeOrigin: true,
        secure: false,
      },
    },
  },
})
```

## 🚀 Quick Start

1. **Start the backend:**
```bash
cd note-manager-backend
source venv/bin/activate
python src/main.py
```

2. **Start the frontend:**
```bash
cd note-manager-frontend
npm run dev
```

3. **Access the application:**
Open http://localhost:5173 in your browser

4. **Import your first notes:**
- Click "Import Notes" button
- Add file paths or directory paths
- Choose whether to process with LLM
- Click "Import"

## 📖 Usage

### Importing Notes

**Individual Files:**
- Specify full paths to .md or .json files
- Support for Obsidian-style frontmatter

**Directories:**
- Recursive directory scanning
- Automatic file type detection
- Preserves directory structure in archive

**LLM Processing:**
- Extract key topics from content
- Generate relevant tags automatically
- Create concise summaries
- Configurable LLM providers via LiteLLM

### Searching Notes

- **Full-text search**: Search across titles, content, and summaries
- **Tag filtering**: Filter by specific tags
- **Real-time results**: Instant search as you type
- **Advanced queries**: Support for complex search patterns

### Graph Visualization

- **Interactive network**: Nodes represent notes, edges show relationships
- **Color coding**: Different colors for .md and .json files
- **Zoom and pan**: Navigate large knowledge graphs
- **Node details**: Click nodes to view note information
- **Relationship discovery**: Visualize connections between topics

### Obsidian Compatibility

- **Frontmatter support**: Preserves YAML frontmatter from Obsidian notes
- **Link detection**: Identifies [[wiki-style]] links between notes
- **Tag inheritance**: Maintains existing tag structure
- **File structure**: Compatible with Obsidian vault organization

## 🔧 Configuration

### LLM Setup

Configure your preferred LLM provider by setting environment variables:

```bash
# OpenAI
export OPENAI_API_KEY="your-api-key"

# Anthropic
export ANTHROPIC_API_KEY="your-api-key"

# Or configure in LLMService for local models
```

### Database Configuration

The system uses SQLite by default. Database location can be configured in `src/main.py`:

```python
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///path/to/your/database.db'
```

### File Storage

Configure storage directories:
- **Notes**: `./notes` (processed files)
- **Archive**: `./archive` (original files)
- **Database**: `./src/database` (SQLite database)

## 🧪 Testing

### API Testing

Test backend endpoints:
```bash
# List notes
curl http://localhost:5000/api/notes

# Search notes
curl "http://localhost:5000/api/notes/search?q=machine%20learning"

# Import files
curl -X POST http://localhost:5000/api/notes/import \
  -H "Content-Type: application/json" \
  -d '{"file_paths": ["/path/to/note.md"], "process_with_llm": false}'

# Get graph data
curl http://localhost:5000/api/notes/graph
```

### Frontend Testing

1. Verify note list loads correctly
2. Test search functionality
3. Import sample files
4. Check graph visualization
5. Test responsive design on mobile

## 🔍 Troubleshooting

### Common Issues

**Database errors:**
```bash
# Ensure database directory exists
mkdir -p src/database
chmod 755 src/database
```

**Import not working:**
- Check file paths are absolute
- Verify file permissions
- Ensure backend is running on port 5000

**Frontend not loading notes:**
- Verify proxy configuration in vite.config.js
- Check browser console for CORS errors
- Ensure backend API is accessible

**LLM processing fails:**
- Verify API keys are set correctly
- Check LiteLLM configuration
- Test with LLM processing disabled first

### Performance Optimization

**Large note collections:**
- Enable pagination in note list
- Use database indexes for search
- Consider chunking large imports

**Graph visualization:**
- Limit nodes displayed for large graphs
- Implement clustering for related notes
- Use efficient SVG rendering

## 🤝 Contributing

This project was developed as a comprehensive solution for local knowledge management. The codebase is designed to be modular and extensible.

### Development Setup

1. Fork the repository
2. Set up development environment
3. Make changes in feature branches
4. Test thoroughly before submitting
5. Follow existing code style and patterns

### Extension Points

- **LLM Providers**: Add new providers via LiteLLM
- **File Types**: Extend import system for new formats
- **Visualization**: Enhance graph rendering with new layouts
- **Search**: Implement advanced search algorithms
- **Export**: Add export functionality for different formats

## 📄 License

This project is provided as-is for educational and personal use. Please ensure compliance with any LLM provider terms of service when using external APIs.

## 🙏 Acknowledgments

- **LiteLLM**: For unified LLM provider interface
- **Flask**: For the robust web framework
- **React**: For the modern frontend framework
- **SQLAlchemy**: For excellent ORM capabilities
- **Obsidian**: For inspiration on knowledge management UX

---

**Built with ❤️ by Manus AI**

For detailed documentation, see [note-manager-documentation.md](./note-manager-documentation.md)

