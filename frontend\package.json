{"name": "note-manager-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "lucide-react": "^0.263.1", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-button": "^1.0.3", "@radix-ui/react-card": "^1.0.4", "@radix-ui/react-badge": "^1.0.4", "@radix-ui/react-input": "^1.0.4", "@radix-ui/react-label": "^1.0.3", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-textarea": "^1.0.4", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slider": "^1.1.2"}, "devDependencies": {"@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@vitejs/plugin-react": "^4.0.3", "eslint": "^8.45.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "vite": "^4.4.5"}}