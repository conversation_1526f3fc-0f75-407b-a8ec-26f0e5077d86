# Security Guide

This document outlines security considerations and best practices for AIrchivist deployment and development.

## Current Security Features

### API Security

#### Rate Limiting
- **Request Limits**: 120 requests/minute, 2000 requests/hour per IP
- **Sliding Window**: Prevents burst attacks and DoS attempts
- **Headers**: Rate limit information exposed in response headers

#### Input Validation
- **Comprehensive Validation**: All API inputs validated using custom validators
- **Type Checking**: Strict type validation for all parameters
- **Sanitization**: Input sanitization to prevent injection attacks
- **Error Handling**: Secure error messages that don't leak sensitive information

#### CORS Configuration
```python
# Secure CORS configuration
CORS(app, origins=[
    "http://localhost:3000",  # Development
    "https://yourdomain.com"  # Production
])
```

### Data Security

#### Database Security
- **SQLAlchemy ORM**: Prevents SQL injection through parameterized queries
- **Connection Security**: Secure database connection strings
- **Data Validation**: Server-side validation for all database operations

#### File Security
- **Path Validation**: Secure file path handling to prevent directory traversal
- **File Type Validation**: Restricted file types for imports
- **Sanitized Filenames**: Filename sanitization to prevent malicious uploads

## Security Best Practices

### Environment Security

#### Environment Variables
```bash
# Secure environment configuration
SECRET_KEY=your-very-long-random-secret-key-here
DATABASE_URI=sqlite:///secure/path/airchivist.db
FLASK_ENV=production  # Never use 'development' in production
DEBUG=false
```

#### File Permissions
```bash
# Secure file permissions
chmod 600 .env                    # Environment file
chmod 755 backend/               # Application directory
chmod 644 backend/src/**/*.py    # Python files
chmod 600 data/database/*        # Database files
```

### Network Security

#### HTTPS Configuration
```nginx
# Nginx HTTPS configuration
server {
    listen 443 ssl http2;
    server_name yourdomain.com;
    
    # SSL Configuration
    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;
    
    # Security Headers
    add_header Strict-Transport-Security "max-age=63072000" always;
    add_header X-Content-Type-Options nosniff;
    add_header X-Frame-Options DENY;
    add_header X-XSS-Protection "1; mode=block";
    add_header Referrer-Policy "strict-origin-when-cross-origin";
}
```

#### Security Headers
```python
# Flask security headers
@app.after_request
def add_security_headers(response):
    response.headers['X-Content-Type-Options'] = 'nosniff'
    response.headers['X-Frame-Options'] = 'DENY'
    response.headers['X-XSS-Protection'] = '1; mode=block'
    response.headers['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains'
    response.headers['Content-Security-Policy'] = "default-src 'self'"
    return response
```

### Application Security

#### Input Validation
```python
# Secure input validation
from marshmallow import Schema, fields, validate

class NoteSchema(Schema):
    title = fields.Str(
        required=True,
        validate=validate.Length(min=1, max=200),
        error_messages={'required': 'Title is required'}
    )
    content = fields.Str(
        required=True,
        validate=validate.Length(min=1, max=50000),
        error_messages={'required': 'Content is required'}
    )
    tags = fields.List(
        fields.Str(validate=validate.Length(min=1, max=50)),
        missing=[],
        validate=validate.Length(max=20)
    )
```

#### SQL Injection Prevention
```python
# Safe database queries using SQLAlchemy ORM
def get_notes_by_search(search_term):
    # This is safe - SQLAlchemy handles parameterization
    return Note.query.filter(
        Note.title.ilike(f"%{search_term}%")
    ).all()

# Avoid raw SQL queries, but if necessary:
def safe_raw_query(user_id):
    # Use parameterized queries
    result = db.session.execute(
        text("SELECT * FROM notes WHERE user_id = :user_id"),
        {"user_id": user_id}
    )
    return result.fetchall()
```

#### File Upload Security
```python
import os
from werkzeug.utils import secure_filename

ALLOWED_EXTENSIONS = {'.md', '.txt', '.json'}
MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB

def secure_file_upload(file):
    # Validate file extension
    filename = secure_filename(file.filename)
    if not any(filename.endswith(ext) for ext in ALLOWED_EXTENSIONS):
        raise ValueError("File type not allowed")
    
    # Validate file size
    file.seek(0, os.SEEK_END)
    file_size = file.tell()
    file.seek(0)
    
    if file_size > MAX_FILE_SIZE:
        raise ValueError("File too large")
    
    return filename
```

### Frontend Security

#### XSS Prevention
```javascript
// Secure content rendering
import DOMPurify from 'dompurify';

function SafeContent({ content }) {
  const sanitizedContent = DOMPurify.sanitize(content);
  return <div dangerouslySetInnerHTML={{ __html: sanitizedContent }} />;
}

// Avoid direct HTML injection
function UnsafeContent({ content }) {
  // DON'T DO THIS
  return <div dangerouslySetInnerHTML={{ __html: content }} />;
}
```

#### CSRF Protection
```javascript
// Include CSRF token in API requests
const csrfToken = document.querySelector('meta[name="csrf-token"]').content;

fetch('/api/notes', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'X-CSRF-Token': csrfToken
  },
  body: JSON.stringify(data)
});
```

## Security Monitoring

### Logging Security Events
```python
import logging
from datetime import datetime

security_logger = logging.getLogger('security')

def log_security_event(event_type, details, request=None):
    """Log security-related events"""
    log_data = {
        'timestamp': datetime.utcnow().isoformat(),
        'event_type': event_type,
        'details': details,
        'ip_address': request.remote_addr if request else None,
        'user_agent': request.headers.get('User-Agent') if request else None
    }
    security_logger.warning(f"Security Event: {log_data}")

# Usage examples
log_security_event('rate_limit_exceeded', {'ip': '***********'})
log_security_event('invalid_file_upload', {'filename': 'malicious.exe'})
```

### Error Handling
```python
# Secure error handling
@app.errorhandler(Exception)
def handle_error(error):
    # Log the full error for debugging
    logger.error(f"Unhandled error: {error}", exc_info=True)
    
    # Return generic error message to user
    if app.debug:
        return jsonify({'error': str(error)}), 500
    else:
        return jsonify({'error': 'Internal server error'}), 500
```

## Deployment Security

### Docker Security
```dockerfile
# Secure Dockerfile
FROM python:3.11-slim

# Create non-root user
RUN groupadd -r appuser && useradd -r -g appuser appuser

# Set working directory
WORKDIR /app

# Copy requirements and install dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Change ownership to non-root user
RUN chown -R appuser:appuser /app

# Switch to non-root user
USER appuser

# Expose port
EXPOSE 5000

# Run application
CMD ["python", "main.py"]
```

### Environment Security
```bash
# Secure environment setup
# Use secrets management for sensitive data
export SECRET_KEY=$(openssl rand -base64 32)
export DATABASE_PASSWORD=$(vault kv get -field=password secret/database)

# Restrict file permissions
umask 077

# Use dedicated user for application
sudo useradd -r -s /bin/false airchivist
sudo chown -R airchivist:airchivist /opt/airchivist
```

## Security Checklist

### Development
- [ ] All inputs validated and sanitized
- [ ] No hardcoded secrets in code
- [ ] Error messages don't leak sensitive information
- [ ] Dependencies regularly updated
- [ ] Security tests included in test suite

### Deployment
- [ ] HTTPS enabled with valid certificates
- [ ] Security headers configured
- [ ] File permissions properly set
- [ ] Non-root user for application
- [ ] Firewall configured to allow only necessary ports

### Monitoring
- [ ] Security event logging enabled
- [ ] Log monitoring and alerting configured
- [ ] Regular security scans scheduled
- [ ] Backup and recovery procedures tested
- [ ] Incident response plan documented

## Security Tools

### Static Analysis
```bash
# Python security scanning
pip install bandit
bandit -r backend/src/

# Dependency vulnerability scanning
pip install safety
safety check

# Frontend security scanning
npm audit
npm audit fix
```

### Runtime Security
```bash
# Container security scanning
docker run --rm -v /var/run/docker.sock:/var/run/docker.sock \
  aquasec/trivy image airchivist:latest

# Network security testing
nmap -sS -O target_host
```

## Incident Response

### Security Incident Procedure
1. **Identify**: Detect and confirm security incident
2. **Contain**: Isolate affected systems
3. **Eradicate**: Remove threat and vulnerabilities
4. **Recover**: Restore systems to normal operation
5. **Learn**: Document lessons learned and improve security

### Emergency Contacts
- Security Team: <EMAIL>
- System Administrator: <EMAIL>
- Legal/Compliance: <EMAIL>

## Regular Security Tasks

### Weekly
- [ ] Review security logs
- [ ] Check for dependency updates
- [ ] Verify backup integrity

### Monthly
- [ ] Security scan of application
- [ ] Review access logs
- [ ] Update security documentation

### Quarterly
- [ ] Penetration testing
- [ ] Security training for team
- [ ] Review and update security policies

## Resources

- [OWASP Top 10](https://owasp.org/www-project-top-ten/)
- [Flask Security Best Practices](https://flask.palletsprojects.com/en/2.0.x/security/)
- [React Security Best Practices](https://snyk.io/blog/10-react-security-best-practices/)
- [Docker Security Best Practices](https://docs.docker.com/engine/security/)

Remember: Security is an ongoing process, not a one-time setup. Regularly review and update security measures as threats evolve.
