# AIrchivist Configuration Example
# Copy this file to .env and modify the values as needed

# Environment (development, production, testing)
FLASK_ENV=development

# Database Configuration
DATABASE_URI=sqlite:///data/database/app.db
# For PostgreSQL: postgresql://user:password@localhost/airchivist
# For MySQL: mysql://user:password@localhost/airchivist

# Security
SECRET_KEY=your-secret-key-here-change-in-production

# Server Configuration
DEBUG=True
HOST=0.0.0.0
PORT=5000

# CORS Configuration (comma-separated origins)
CORS_ORIGINS=*

# File Storage Directories
NOTES_DIRECTORY=data/notes
ARCHIVE_DIRECTORY=data/archive
DATABASE_DIRECTORY=data/database
LOGS_DIRECTORY=data/logs

# LLM Configuration
LLM_MODEL_NAME=ollama/llama3.2
LLM_API_BASE=http://localhost:11434
LLM_TEMPERATURE=0.3
LLM_MAX_TOKENS=2000

# External LLM API Keys (optional)
# OPENAI_API_KEY=your-openai-api-key
# ANTHROPIC_API_KEY=your-anthropic-api-key

# Logging Configuration
LOG_LEVEL=INFO
# LOG_FILE=custom/path/to/logfile.log

# API Configuration
DEFAULT_PAGE_SIZE=20
MAX_PAGE_SIZE=100

# File Processing Limits
MAX_FILE_SIZE_MB=10
SUPPORTED_FILE_TYPES=.md,.json

# Database Debug (set to True to see SQL queries)
SQLALCHEMY_ECHO=False
