import React from 'react'

export const Slider = ({ 
  value = [0], 
  onValueChange, 
  max = 100, 
  min = 0, 
  step = 1, 
  className = '',
  ...props 
}) => {
  const handleChange = (e) => {
    const newValue = [parseInt(e.target.value)]
    onValueChange?.(newValue)
  }

  return (
    <div className={`relative flex w-full touch-none select-none items-center ${className}`}>
      <input
        type="range"
        min={min}
        max={max}
        step={step}
        value={value[0]}
        onChange={handleChange}
        className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
        {...props}
      />
    </div>
  )
}
