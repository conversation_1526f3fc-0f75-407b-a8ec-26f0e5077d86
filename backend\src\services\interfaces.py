"""
Service interfaces for dependency injection and abstraction
"""
from abc import ABC, abstractmethod
from typing import List, Dict, Optional, Any
from src.models import Note, Tag, ArchivedFile


class ILLMService(ABC):
    """Interface for LLM service implementations"""
    
    @abstractmethod
    def test_connection(self) -> bool:
        """Test connection to LLM service"""
        pass
    
    @abstractmethod
    def summarize_content(self, content: str) -> str:
        """Generate a summary of the content"""
        pass
    
    @abstractmethod
    def extract_topics(self, content: str) -> List[str]:
        """Extract topics from content"""
        pass
    
    @abstractmethod
    def generate_tags(self, content: str, existing_tags: List[str] = None) -> List[str]:
        """Generate tags for content"""
        pass
    
    @abstractmethod
    def analyze_content(self, content: str) -> Dict[str, Any]:
        """Perform comprehensive content analysis"""
        pass


class IFileService(ABC):
    """Interface for file service implementations"""
    
    @abstractmethod
    def import_file(self, file_path: str, process_with_llm: bool = True) -> Optional[Note]:
        """Import a single file"""
        pass
    
    @abstractmethod
    def import_directory(self, directory_path: str, recursive: bool = True, 
                        process_with_llm: bool = True) -> List[Note]:
        """Import files from a directory"""
        pass
    
    @abstractmethod
    def update_note_content(self, note_id: int, content: str, 
                           process_with_llm: bool = True) -> Optional[Note]:
        """Update note content"""
        pass
    
    @abstractmethod
    def archive_file(self, file_path: str, content: str) -> Optional[ArchivedFile]:
        """Archive a file"""
        pass
    
    @abstractmethod
    def get_notes_directory(self) -> str:
        """Get the notes directory path"""
        pass
    
    @abstractmethod
    def set_notes_directory(self, directory_path: str) -> None:
        """Set the notes directory path"""
        pass


class ITagService(ABC):
    """Interface for tag service implementations"""
    
    @abstractmethod
    def get_all_tags(self) -> List[Tag]:
        """Get all tags"""
        pass
    
    @abstractmethod
    def create_tag(self, name: str, color: str = None) -> Tag:
        """Create a new tag"""
        pass
    
    @abstractmethod
    def update_tag(self, tag_id: int, name: str = None, color: str = None) -> Optional[Tag]:
        """Update a tag"""
        pass
    
    @abstractmethod
    def delete_tag(self, tag_id: int) -> bool:
        """Delete a tag"""
        pass
    
    @abstractmethod
    def get_or_create_tag(self, name: str, color: str = None) -> Tag:
        """Get existing tag or create new one"""
        pass


class INoteService(ABC):
    """Interface for note service implementations"""
    
    @abstractmethod
    def get_notes(self, page: int = 1, per_page: int = 20, search: str = None, 
                  tag_filter: str = None) -> Dict[str, Any]:
        """Get notes with pagination and filtering"""
        pass
    
    @abstractmethod
    def get_note(self, note_id: int) -> Optional[Note]:
        """Get a specific note"""
        pass
    
    @abstractmethod
    def create_note(self, title: str, content: str, tags: List[str] = None) -> Note:
        """Create a new note"""
        pass
    
    @abstractmethod
    def update_note(self, note_id: int, title: str = None, content: str = None, 
                    tags: List[str] = None) -> Optional[Note]:
        """Update a note"""
        pass
    
    @abstractmethod
    def delete_note(self, note_id: int) -> bool:
        """Delete a note"""
        pass
    
    @abstractmethod
    def get_graph_data(self) -> Dict[str, Any]:
        """Get graph data for visualization"""
        pass


class IServiceContainer(ABC):
    """Interface for service container/dependency injection"""
    
    @abstractmethod
    def get_llm_service(self) -> ILLMService:
        """Get LLM service instance"""
        pass
    
    @abstractmethod
    def get_file_service(self) -> IFileService:
        """Get file service instance"""
        pass
    
    @abstractmethod
    def get_tag_service(self) -> ITagService:
        """Get tag service instance"""
        pass
    
    @abstractmethod
    def get_note_service(self) -> INoteService:
        """Get note service instance"""
        pass
