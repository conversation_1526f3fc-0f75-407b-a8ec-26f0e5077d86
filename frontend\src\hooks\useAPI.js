/**
 * Custom hooks for API calls with loading and error states
 */
import { useState, useEffect, useCallback, useRef } from 'react';
import { APIError } from '../services/api';

/**
 * Generic hook for API calls with loading and error states
 */
export function useAPI(apiFunction, dependencies = [], options = {}) {
  const [data, setData] = useState(options.initialData || null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [lastFetch, setLastFetch] = useState(null);
  
  const abortControllerRef = useRef(null);
  const {
    immediate = true,
    onSuccess,
    onError,
    retryCount = 0,
    retryDelay = 1000,
  } = options;

  const execute = useCallback(async (...args) => {
    // Cancel previous request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // Create new abort controller
    abortControllerRef.current = new AbortController();
    
    setLoading(true);
    setError(null);

    let attempts = 0;
    const maxAttempts = retryCount + 1;

    while (attempts < maxAttempts) {
      try {
        const result = await apiFunction(...args);
        
        if (!abortControllerRef.current.signal.aborted) {
          setData(result);
          setLastFetch(new Date());
          setLoading(false);
          
          if (onSuccess) {
            onSuccess(result);
          }
          
          return result;
        }
      } catch (err) {
        attempts++;
        
        if (abortControllerRef.current.signal.aborted) {
          return;
        }

        if (attempts >= maxAttempts) {
          setError(err);
          setLoading(false);
          
          if (onError) {
            onError(err);
          }
          
          throw err;
        } else {
          // Wait before retry
          await new Promise(resolve => setTimeout(resolve, retryDelay * attempts));
        }
      }
    }
  }, [apiFunction, retryCount, retryDelay, onSuccess, onError]);

  const refresh = useCallback(() => {
    return execute();
  }, [execute]);

  const reset = useCallback(() => {
    setData(options.initialData || null);
    setLoading(false);
    setError(null);
    setLastFetch(null);
    
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
  }, [options.initialData]);

  useEffect(() => {
    if (immediate) {
      execute();
    }

    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, dependencies);

  return {
    data,
    loading,
    error,
    execute,
    refresh,
    reset,
    lastFetch,
  };
}

/**
 * Hook for paginated API calls
 */
export function usePaginatedAPI(apiFunction, options = {}) {
  const [page, setPage] = useState(options.initialPage || 1);
  const [perPage, setPerPage] = useState(options.initialPerPage || 20);
  const [allData, setAllData] = useState([]);
  const [hasMore, setHasMore] = useState(true);
  const [totalCount, setTotalCount] = useState(0);

  const {
    data,
    loading,
    error,
    execute,
    refresh: refreshPage,
  } = useAPI(
    () => apiFunction({ page, per_page: perPage, ...options.params }),
    [page, perPage, ...(options.dependencies || [])],
    {
      immediate: options.immediate !== false,
      onSuccess: (result) => {
        if (result.success && result.data) {
          const items = result.data;
          const pagination = result.meta?.pagination;
          
          if (page === 1) {
            setAllData(items);
          } else {
            setAllData(prev => [...prev, ...items]);
          }
          
          if (pagination) {
            setHasMore(pagination.has_next);
            setTotalCount(pagination.total);
          }
        }
        
        if (options.onSuccess) {
          options.onSuccess(result);
        }
      },
      onError: options.onError,
    }
  );

  const loadMore = useCallback(() => {
    if (!loading && hasMore) {
      setPage(prev => prev + 1);
    }
  }, [loading, hasMore]);

  const refresh = useCallback(() => {
    setPage(1);
    setAllData([]);
    setHasMore(true);
    setTotalCount(0);
    return refreshPage();
  }, [refreshPage]);

  const changePerPage = useCallback((newPerPage) => {
    setPerPage(newPerPage);
    setPage(1);
    setAllData([]);
    setHasMore(true);
  }, []);

  return {
    data: allData,
    currentPageData: data?.data || [],
    loading,
    error,
    page,
    perPage,
    hasMore,
    totalCount,
    loadMore,
    refresh,
    changePerPage,
    setPage,
  };
}

/**
 * Hook for mutations (POST, PUT, DELETE)
 */
export function useMutation(mutationFunction, options = {}) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [data, setData] = useState(null);

  const mutate = useCallback(async (...args) => {
    setLoading(true);
    setError(null);

    try {
      const result = await mutationFunction(...args);
      setData(result);
      setLoading(false);

      if (options.onSuccess) {
        options.onSuccess(result);
      }

      return result;
    } catch (err) {
      setError(err);
      setLoading(false);

      if (options.onError) {
        options.onError(err);
      }

      throw err;
    }
  }, [mutationFunction, options.onSuccess, options.onError]);

  const reset = useCallback(() => {
    setLoading(false);
    setError(null);
    setData(null);
  }, []);

  return {
    mutate,
    loading,
    error,
    data,
    reset,
  };
}

/**
 * Hook for optimistic updates
 */
export function useOptimisticMutation(mutationFunction, options = {}) {
  const [optimisticData, setOptimisticData] = useState(null);
  const [isOptimistic, setIsOptimistic] = useState(false);
  
  const { mutate, loading, error, data, reset } = useMutation(mutationFunction, {
    onSuccess: (result) => {
      setOptimisticData(null);
      setIsOptimistic(false);
      if (options.onSuccess) {
        options.onSuccess(result);
      }
    },
    onError: (err) => {
      setOptimisticData(null);
      setIsOptimistic(false);
      if (options.onError) {
        options.onError(err);
      }
    },
  });

  const optimisticMutate = useCallback(async (optimisticValue, ...args) => {
    setOptimisticData(optimisticValue);
    setIsOptimistic(true);
    
    try {
      return await mutate(...args);
    } catch (err) {
      // Error handling is done in the mutation hook
      throw err;
    }
  }, [mutate]);

  const resetOptimistic = useCallback(() => {
    setOptimisticData(null);
    setIsOptimistic(false);
    reset();
  }, [reset]);

  return {
    mutate: optimisticMutate,
    loading,
    error,
    data: isOptimistic ? optimisticData : data,
    isOptimistic,
    reset: resetOptimistic,
  };
}

/**
 * Hook for debounced API calls (useful for search)
 */
export function useDebouncedAPI(apiFunction, delay = 300, dependencies = [], options = {}) {
  const [debouncedDeps, setDebouncedDeps] = useState(dependencies);
  const timeoutRef = useRef(null);

  useEffect(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    timeoutRef.current = setTimeout(() => {
      setDebouncedDeps(dependencies);
    }, delay);

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, dependencies);

  return useAPI(apiFunction, debouncedDeps, options);
}

/**
 * Hook for handling API errors with user-friendly messages
 */
export function useAPIError() {
  const getErrorMessage = useCallback((error) => {
    if (error instanceof APIError) {
      switch (error.status) {
        case 400:
          return error.message || 'Invalid request. Please check your input.';
        case 401:
          return 'You are not authorized to perform this action.';
        case 403:
          return 'You do not have permission to access this resource.';
        case 404:
          return 'The requested resource was not found.';
        case 429:
          return error.message || 'Too many requests. Please try again later.';
        case 500:
          return 'Server error. Please try again later.';
        default:
          return error.message || 'An unexpected error occurred.';
      }
    }
    
    if (error.message) {
      return error.message;
    }
    
    return 'An unexpected error occurred.';
  }, []);

  const isRetryable = useCallback((error) => {
    if (error instanceof APIError) {
      return [408, 429, 500, 502, 503, 504].includes(error.status);
    }
    return true; // Network errors are generally retryable
  }, []);

  return { getErrorMessage, isRetryable };
}
}
