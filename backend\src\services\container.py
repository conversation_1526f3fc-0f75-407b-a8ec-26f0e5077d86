"""
Service container for dependency injection
"""
from typing import Optional, Dict, Any
from src.services.interfaces import IServiceContainer, ILLMService, IFileService, ITagService, INoteService
from src.utils.logging_config import get_logger

logger = get_logger('airchivist.service_container')


class ServiceContainer(IServiceContainer):
    """
    Service container implementing dependency injection pattern
    """
    
    def __init__(self):
        self._services: Dict[str, Any] = {}
        self._singletons: Dict[str, bool] = {}
        self._factories: Dict[str, callable] = {}
        
    def register_singleton(self, service_name: str, factory: callable) -> None:
        """
        Register a service as singleton
        
        Args:
            service_name: Name of the service
            factory: Factory function to create the service
        """
        self._factories[service_name] = factory
        self._singletons[service_name] = True
        logger.debug(f"Registered singleton service: {service_name}")
    
    def register_transient(self, service_name: str, factory: callable) -> None:
        """
        Register a service as transient (new instance each time)
        
        Args:
            service_name: Name of the service
            factory: Factory function to create the service
        """
        self._factories[service_name] = factory
        self._singletons[service_name] = False
        logger.debug(f"Registered transient service: {service_name}")
    
    def get_service(self, service_name: str) -> Any:
        """
        Get a service instance
        
        Args:
            service_name: Name of the service
            
        Returns:
            Service instance
            
        Raises:
            ValueError: If service is not registered
        """
        if service_name not in self._factories:
            raise ValueError(f"Service '{service_name}' is not registered")
        
        # Check if singleton and already created
        if self._singletons[service_name] and service_name in self._services:
            return self._services[service_name]
        
        # Create new instance
        try:
            service_instance = self._factories[service_name](self)
            
            # Store if singleton
            if self._singletons[service_name]:
                self._services[service_name] = service_instance
            
            logger.debug(f"Created service instance: {service_name}")
            return service_instance
            
        except Exception as e:
            logger.error(f"Failed to create service '{service_name}': {e}", exc_info=True)
            raise
    
    def get_llm_service(self) -> ILLMService:
        """Get LLM service instance"""
        return self.get_service('llm_service')
    
    def get_file_service(self) -> IFileService:
        """Get file service instance"""
        return self.get_service('file_service')
    
    def get_tag_service(self) -> ITagService:
        """Get tag service instance"""
        return self.get_service('tag_service')
    
    def get_note_service(self) -> INoteService:
        """Get note service instance"""
        return self.get_service('note_service')
    
    def clear_singletons(self) -> None:
        """Clear all singleton instances (useful for testing)"""
        self._services.clear()
        logger.debug("Cleared all singleton instances")


# Global service container instance
_container: Optional[ServiceContainer] = None


def get_container() -> ServiceContainer:
    """
    Get the global service container instance
    
    Returns:
        ServiceContainer instance
    """
    global _container
    if _container is None:
        _container = ServiceContainer()
        _configure_container(_container)
    return _container


def _configure_container(container: ServiceContainer) -> None:
    """
    Configure the service container with default services
    
    Args:
        container: ServiceContainer to configure
    """
    from src.services.llm_service import LLMService
    from src.services.file_service import FileService
    from src.services.tag_service import TagService
    from src.services.note_service import NoteService
    
    # Register services as singletons
    container.register_singleton('llm_service', lambda c: LLMService())
    container.register_singleton('file_service', lambda c: FileService(llm_service=c.get_llm_service()))
    container.register_singleton('tag_service', lambda c: TagService())
    container.register_singleton('note_service', lambda c: NoteService(
        llm_service=c.get_llm_service(),
        tag_service=c.get_tag_service()
    ))
    
    logger.info("Service container configured with default services")


def reset_container() -> None:
    """Reset the global container (useful for testing)"""
    global _container
    _container = None
