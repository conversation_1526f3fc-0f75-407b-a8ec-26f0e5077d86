# Note Manager: Local Knowledge Management System

**Author:** Manus AI  
**Version:** 1.0  
**Date:** July 11, 2025

## Executive Summary

The Note Manager is a comprehensive local knowledge management system designed to handle large collections of notes and LLM conversations in both Markdown (.md) and JSON (.json) file formats. This system provides advanced features including LLM-powered content analysis, intelligent tagging and summarization, user-friendly search capabilities, and interactive graph visualization of knowledge relationships. Built with 100% local operation in mind, the system ensures complete data privacy while maintaining compatibility with popular note-taking applications like Obsidian.

The system architecture consists of a Flask-based backend API that handles file processing, database management, and LLM integration through LiteLLM, paired with a modern React frontend that provides an intuitive user interface for browsing, searching, and visualizing notes. The graph visualization component offers an Obsidian-style network view of note relationships, enabling users to discover connections and patterns within their knowledge base.

Key features include automated file import from directories or individual files, intelligent content processing using configurable LLM providers, full-text search across note content and metadata, tag-based organization, and a separate archival database that preserves original files. The system maintains full compatibility with Obsidian's file structure and metadata conventions, allowing seamless integration with existing workflows.

## Table of Contents

1. [System Architecture](#system-architecture)
2. [Installation and Setup](#installation-and-setup)
3. [Core Features](#core-features)
4. [API Documentation](#api-documentation)
5. [Frontend Components](#frontend-components)
6. [Database Schema](#database-schema)
7. [LLM Integration](#llm-integration)
8. [File Import System](#file-import-system)
9. [Graph Visualization](#graph-visualization)
10. [Obsidian Compatibility](#obsidian-compatibility)
11. [Testing and Quality Assurance](#testing-and-quality-assurance)
12. [Deployment Guide](#deployment-guide)
13. [Troubleshooting](#troubleshooting)
14. [Future Enhancements](#future-enhancements)
15. [References](#references)




## System Architecture

The Note Manager employs a modern three-tier architecture that separates concerns between data storage, business logic, and user interface presentation. This architectural approach ensures scalability, maintainability, and clear separation of responsibilities across the system components.

### Backend Architecture

The backend is built using Flask, a lightweight Python web framework that provides the foundation for the REST API. Flask was chosen for its simplicity, flexibility, and extensive ecosystem of extensions that support database integration, CORS handling, and API development. The backend architecture follows a modular design pattern with distinct layers for models, services, and routes.

The data layer utilizes SQLAlchemy as the Object-Relational Mapping (ORM) tool, providing a Python-based interface to the SQLite database. SQLite was selected as the database engine due to its serverless nature, zero-configuration requirements, and excellent performance for local applications. The database schema includes tables for notes, tags, note-tag relationships, note links, and archived files, with proper foreign key constraints to maintain data integrity.

The service layer contains the core business logic, including the FileService for handling file operations and imports, and the LLMService for managing interactions with language models. This layer abstracts the complexity of file processing, content analysis, and database operations from the API routes, promoting code reusability and easier testing.

The API layer consists of Flask blueprints that define RESTful endpoints for note management, file import, search functionality, and graph data generation. Each endpoint follows REST conventions with appropriate HTTP methods and status codes, ensuring a predictable and standards-compliant interface.

### Frontend Architecture

The frontend is developed using React 18 with modern functional components and hooks, providing a responsive and interactive user interface. The application uses Vite as the build tool and development server, offering fast hot module replacement and optimized production builds. The component architecture follows a hierarchical structure with clear separation between presentation and logic components.

The main application component (App.jsx) serves as the root container that manages global state and routing between different views. The interface is organized into tabs for Notes and Graph View, with each tab containing specialized components for their respective functionality. The Notes tab includes the NoteList component for browsing and searching notes, and the NoteViewer component for displaying and editing note content.

The Graph View tab contains the GraphView component, which implements an interactive network visualization using SVG and custom JavaScript for node positioning and interaction handling. This component processes graph data from the backend API and renders nodes representing notes with edges showing relationships between them.

State management is handled through React's built-in useState and useEffect hooks, with API calls managed through the fetch API. The application includes proper error handling and loading states to provide feedback to users during asynchronous operations.

### Integration Layer

The integration between frontend and backend is facilitated through a proxy configuration in Vite that routes API calls from the React development server to the Flask backend. This setup enables seamless development while maintaining clear separation between the two services. In production, both services can be deployed together or separately depending on infrastructure requirements.

Cross-Origin Resource Sharing (CORS) is properly configured on the backend to allow requests from the frontend, with Flask-CORS providing the necessary headers for browser security compliance. The API follows RESTful conventions with JSON request and response formats, ensuring compatibility with modern web development practices.

### LLM Integration Architecture

The LLM integration is implemented through LiteLLM, a unified interface that supports multiple language model providers including OpenAI, Anthropic, Google, and local models. This abstraction layer allows users to configure their preferred LLM provider without requiring code changes, providing flexibility and vendor independence.

The LLMService component handles all interactions with language models, including topic extraction, tag generation, and content summarization. The service implements proper error handling and fallback mechanisms to ensure system stability when LLM services are unavailable or rate-limited.

### File System Architecture

The file system architecture maintains a clear separation between original files and processed content. Original files are archived in a dedicated directory structure that preserves the source file hierarchy and metadata. Processed files are stored in the notes directory with standardized naming conventions to prevent conflicts.

The import system supports both individual file imports and recursive directory scanning, with configurable options for LLM processing and file filtering. The system maintains a mapping between original file locations and processed note entries in the database, enabling traceability and potential re-import scenarios.

### Security Considerations

The system implements several security measures to protect user data and ensure safe operation. All file operations are performed with proper path validation to prevent directory traversal attacks. Database queries use parameterized statements through SQLAlchemy to prevent SQL injection vulnerabilities.

The local-first architecture ensures that sensitive note content never leaves the user's machine unless explicitly configured to use cloud-based LLM services. When LLM processing is enabled, only the necessary content is sent to the configured provider, with no persistent storage of user data on external servers.

### Performance Optimization

The system includes several performance optimizations to ensure responsive operation even with large note collections. Database queries are optimized with appropriate indexes on frequently searched fields. The frontend implements pagination for note lists and lazy loading for note content to minimize initial load times.

The graph visualization component uses efficient algorithms for node positioning and includes zoom and pan controls to handle large networks of notes. SVG rendering is optimized for performance with proper event handling and minimal DOM manipulation during interactions.


## Installation and Setup

The Note Manager system requires a Python environment for the backend and Node.js for the frontend development and build process. The following sections provide detailed instructions for setting up the system on various operating systems.

### Prerequisites

Before installing the Note Manager, ensure that your system meets the following requirements:

**System Requirements:**
- Operating System: Linux (Ubuntu 20.04+), macOS (10.15+), or Windows 10+
- Python: Version 3.8 or higher with pip package manager
- Node.js: Version 16.0 or higher with npm or pnpm package manager
- Available disk space: Minimum 500MB for installation, additional space for note storage
- Memory: Minimum 4GB RAM recommended for optimal performance

**Optional Requirements:**
- Git: For cloning the repository and version control
- LLM API access: OpenAI API key, Anthropic API key, or local model setup for content processing
- Web browser: Modern browser supporting ES6+ for frontend access

### Backend Installation

The backend installation process involves setting up the Python environment, installing dependencies, and configuring the database. Follow these steps to install the backend components:

**Step 1: Create Project Directory**
```bash
mkdir note-manager
cd note-manager
```

**Step 2: Set Up Python Virtual Environment**
```bash
python3 -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

**Step 3: Install Backend Dependencies**
```bash
pip install flask flask-sqlalchemy flask-cors litellm python-frontmatter
```

**Step 4: Create Directory Structure**
The backend requires specific directories for proper operation:
```bash
mkdir -p src/models src/routes src/services src/database notes archive
```

**Step 5: Database Initialization**
The SQLite database is automatically created when the application first runs. The database file will be located at `src/database/app.db` and will contain all note metadata, tags, and relationships.

### Frontend Installation

The frontend installation involves setting up the Node.js environment and installing the React application dependencies:

**Step 1: Create Frontend Directory**
```bash
mkdir note-manager-frontend
cd note-manager-frontend
```

**Step 2: Initialize React Application**
If using the provided template system:
```bash
manus-create-react-app note-manager-frontend
```

Alternatively, create a new React application manually:
```bash
npx create-react-app . --template typescript
```

**Step 3: Install Frontend Dependencies**
```bash
npm install lucide-react
# or using pnpm
pnpm install lucide-react
```

**Step 4: Configure Development Proxy**
Create or update `vite.config.js` to include proxy configuration for API calls:
```javascript
import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'

export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
  server: {
    proxy: {
      '/api': {
        target: 'http://localhost:5000',
        changeOrigin: true,
        secure: false,
      },
    },
  },
})
```

### Configuration

The system requires minimal configuration for basic operation, with optional settings for advanced features:

**Backend Configuration:**
The main configuration is handled in `src/main.py`:
```python
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///path/to/database/app.db'
app.config['SECRET_KEY'] = 'your-secret-key-here'
```

**LLM Configuration:**
LLM integration is configured through environment variables or direct configuration in the LLMService:
```bash
export OPENAI_API_KEY="your-openai-api-key"
export ANTHROPIC_API_KEY="your-anthropic-api-key"
```

**File Storage Configuration:**
The system uses configurable directories for note storage and archival:
- Notes directory: `./notes` (processed files)
- Archive directory: `./archive` (original files)
- Database directory: `./src/database` (SQLite database)

### Running the System

To start the complete system, both backend and frontend services must be running:

**Start Backend Service:**
```bash
cd note-manager-backend
source venv/bin/activate
python src/main.py
```
The backend will start on `http://localhost:5000` by default.

**Start Frontend Service:**
```bash
cd note-manager-frontend
npm run dev
# or using pnpm
pnpm run dev
```
The frontend will start on `http://localhost:5173` by default.

**Access the Application:**
Open your web browser and navigate to `http://localhost:5173` to access the Note Manager interface.

### Initial Setup and Testing

After installation, perform these steps to verify the system is working correctly:

**Step 1: Verify Backend API**
Test the backend API by accessing `http://localhost:5000/api/notes` in your browser or using curl:
```bash
curl http://localhost:5000/api/notes
```

**Step 2: Test File Import**
Create a sample Markdown file and test the import functionality through the web interface or API:
```bash
curl -X POST http://localhost:5000/api/notes/import \
  -H "Content-Type: application/json" \
  -d '{"file_paths": ["/path/to/sample.md"], "process_with_llm": false}'
```

**Step 3: Verify Frontend Connectivity**
Access the web interface and confirm that the notes list loads properly and displays any imported notes.

### Troubleshooting Installation

Common installation issues and their solutions:

**Database Permission Errors:**
Ensure the application has write permissions to the database directory:
```bash
chmod 755 src/database
```

**Port Conflicts:**
If the default ports are in use, modify the configuration:
- Backend: Change the port in `src/main.py`: `app.run(host='0.0.0.0', port=5001)`
- Frontend: Use `--port` flag: `npm run dev -- --port 3001`

**Python Module Import Errors:**
Verify the virtual environment is activated and all dependencies are installed:
```bash
pip list | grep flask
pip install --upgrade -r requirements.txt
```

**Node.js Dependency Issues:**
Clear the package cache and reinstall dependencies:
```bash
rm -rf node_modules package-lock.json
npm install
```

