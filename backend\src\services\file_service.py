import hashlib
import json
import os
import shutil
from datetime import datetime
from typing import Any, Dict, List, Optional

import frontmatter

from src.config import get_config
from src.models import ArchivedFile, Note, Tag, db
from src.services.interfaces import IFileService
from src.services.llm_service import LLMService
from src.utils.logging_config import get_logger


class FileService(IFileService):
    def __init__(self, llm_service: LLMService = None, notes_directory: str = None, archive_directory: str = None):
        """
        Initialize FileService with configurable directories

        Args:
            llm_service: LLMService instance for content processing
            notes_directory: Directory to store processed notes
            archive_directory: Directory to store archived original files
        """
        self.llm_service = llm_service or LLMService()
        self.logger = get_logger('airchivist.file_service')

        # Get configuration
        config = get_config()

        # Get project root (parent of backend)
        project_root = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))

        # Set default directories from configuration
        self.notes_directory = notes_directory or config.get_absolute_path(config.NOTES_DIRECTORY, project_root)
        self.archive_directory = archive_directory or config.get_absolute_path(config.ARCHIVE_DIRECTORY, project_root)

        # Ensure directories exist
        os.makedirs(self.notes_directory, exist_ok=True)
        os.makedirs(self.archive_directory, exist_ok=True)

    def get_notes_directory(self) -> str:
        """Get the current notes directory"""
        return self.notes_directory

    def set_notes_directory(self, directory: str):
        """Set the notes directory"""
        if os.path.exists(directory):
            self.notes_directory = directory
            os.makedirs(self.notes_directory, exist_ok=True)

    def import_file(self, file_path: str, process_with_llm: bool = True) -> Optional[Note]:
        """
        Import a single file into the note system

        Args:
            file_path: Path to the file to import
            process_with_llm: Whether to process with LLM for topics/tags/summary

        Returns:
            Note object if successful, None otherwise
        """
        try:
            if not os.path.exists(file_path):
                self.logger.warning(f"File not found: {file_path}")
                return None

            # Check if file is supported
            file_extension = os.path.splitext(file_path)[1].lower()
            if file_extension not in ['.md', '.json']:
                self.logger.warning(f"Unsupported file type: {file_extension}")
                return None

            # Read file content
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # Archive original file
            self._archive_file(file_path, content)

            # Copy file to notes directory
            filename = os.path.basename(file_path)
            target_path = os.path.join(self.notes_directory, filename)

            # Handle filename conflicts
            counter = 1
            base_name, ext = os.path.splitext(filename)
            while os.path.exists(target_path):
                new_filename = f"{base_name}_{counter}{ext}"
                target_path = os.path.join(self.notes_directory, new_filename)
                counter += 1

            shutil.copy2(file_path, target_path)

            # Process file content based on type
            if file_extension == '.md':
                note = self._process_markdown_file(target_path, content, process_with_llm)
            else:  # .json
                note = self._process_json_file(target_path, content, process_with_llm)

            return note

        except Exception as e:
            self.logger.error(f"Error importing file {file_path}: {e}", exc_info=True)
            return None

    def import_directory(self, directory_path: str, recursive: bool = True, process_with_llm: bool = True) -> List[Note]:
        """
        Import all supported files from a directory

        Args:
            directory_path: Path to the directory to import
            recursive: Whether to scan subdirectories
            process_with_llm: Whether to process with LLM

        Returns:
            List of imported Note objects
        """
        imported_notes = []

        try:
            if not os.path.exists(directory_path):
                self.logger.warning(f"Directory not found: {directory_path}")
                return imported_notes

            # Walk through directory
            for root, _, files in os.walk(directory_path):
                for file in files:
                    file_path = os.path.join(root, file)
                    file_extension = os.path.splitext(file)[1].lower()

                    if file_extension in ['.md', '.json']:
                        note = self.import_file(file_path, process_with_llm)
                        if note:
                            imported_notes.append(note)

                # If not recursive, only process the top level
                if not recursive:
                    break

        except Exception as e:
            self.logger.error(f"Error importing directory {directory_path}: {e}", exc_info=True)

        return imported_notes

    def update_note_content(self, note_id: int, content: str, process_with_llm: bool = True) -> Optional[Note]:
        """
        Update a note's content and reprocess if needed

        Args:
            note_id: ID of the note to update
            content: New content
            process_with_llm: Whether to reprocess with LLM

        Returns:
            Updated Note object if successful, None otherwise
        """
        try:
            note = Note.query.get(note_id)
            if not note:
                return None

            # Update content
            note.content = content
            note.updated_at = datetime.utcnow()

            # Update file on disk
            with open(note.file_path, 'w', encoding='utf-8') as f:
                f.write(content)

            # Reprocess with LLM if requested
            if process_with_llm and self.llm_service:
                self._process_with_llm(note, content)

            db.session.commit()
            return note

        except Exception as e:
            print(f"Error updating note {note_id}: {e}")
            db.session.rollback()
            return None

    def _archive_file(self, file_path: str, content: str) -> Optional['ArchivedFile']:
        """
        Archive the original file content

        Args:
            file_path: Original file path
            content: File content

        Returns:
            ArchivedFile object if successful, None otherwise
        """
        try:
            # Calculate file hash
            file_hash = hashlib.sha256(content.encode('utf-8')).hexdigest()

            # Check if already archived
            existing = ArchivedFile.query.filter_by(file_hash=file_hash).first()
            if existing:
                return existing

            # Create archive entry
            archived_file = ArchivedFile(
                original_path=file_path,
                content=content,
                file_hash=file_hash,
                file_size=len(content.encode('utf-8'))
            )

            db.session.add(archived_file)
            db.session.commit()

            return archived_file

        except Exception as e:
            self.logger.error(f"Error archiving file {file_path}: {e}", exc_info=True)
            db.session.rollback()
            return None

    def _process_markdown_file(self, file_path: str, content: str, process_with_llm: bool = True) -> Optional[Note]:
        """
        Process a markdown file and create a Note entry

        Args:
            file_path: Path to the file
            content: File content
            process_with_llm: Whether to process with LLM

        Returns:
            Note object if successful, None otherwise
        """
        try:
            # Parse frontmatter if present
            try:
                post = frontmatter.loads(content)
                title = post.metadata.get('title', os.path.splitext(os.path.basename(file_path))[0])
                content_text = post.content
            except:
                # No frontmatter, use filename as title
                title = os.path.splitext(os.path.basename(file_path))[0]
                content_text = content

            # Create note entry
            note = Note(
                title=title,
                content=content_text,
                file_path=file_path,
                file_type='md'
            )

            # Process with LLM if requested
            if process_with_llm and self.llm_service:
                self._process_with_llm(note, content_text)

            db.session.add(note)
            db.session.commit()

            return note

        except Exception as e:
            self.logger.error(f"Error processing markdown file {file_path}: {e}", exc_info=True)
            db.session.rollback()
            return None

    def _process_json_file(self, file_path: str, content: str, process_with_llm: bool = True) -> Optional[Note]:
        """
        Process a JSON file (conversation) and create a Note entry

        Args:
            file_path: Path to the file
            content: File content
            process_with_llm: Whether to process with LLM

        Returns:
            Note object if successful, None otherwise
        """
        try:
            # Parse JSON content
            data = json.loads(content)

            # Extract title and content from conversation
            title = data.get('title', os.path.splitext(os.path.basename(file_path))[0])

            # Convert conversation to readable text
            content_text = self._extract_conversation_text(data)

            # Create note entry
            note = Note(
                title=title,
                content=content_text,
                file_path=file_path,
                file_type='json'
            )

            # Process with LLM if requested
            if process_with_llm and self.llm_service:
                self._process_with_llm(note, content_text)

            db.session.add(note)
            db.session.commit()

            return note

        except Exception as e:
            self.logger.error(f"Error processing JSON file {file_path}: {e}", exc_info=True)
            db.session.rollback()
            return None

    def _extract_conversation_text(self, data: Dict[Any, Any]) -> str:
        """
        Extract readable text from conversation JSON

        Args:
            data: Parsed JSON data

        Returns:
            Formatted conversation text
        """
        try:
            text_parts = []

            # Add title if present
            if 'title' in data:
                text_parts.append(f"# {data['title']}\n")

            # Extract messages/conversation
            messages = data.get('messages', data.get('conversation', []))

            for message in messages:
                if isinstance(message, dict):
                    role = message.get('role', 'unknown')
                    content = message.get('content', '')
                    text_parts.append(f"**{role.title()}:** {content}\n")
                elif isinstance(message, str):
                    text_parts.append(f"{message}\n")

            return '\n'.join(text_parts)

        except Exception as e:
            self.logger.error(f"Error extracting conversation text: {e}", exc_info=True)
            return str(data)

    def _process_with_llm(self, note: Note, content: str):
        """
        Process note content with LLM to extract topics, tags, and summary

        Args:
            note: Note object to update
            content: Content to process
        """
        try:
            if not self.llm_service:
                return

            # Extract topics
            topics = self.llm_service.extract_topics(content)
            note.topics = json.dumps(topics) if topics else None

            # Generate summary
            summary = self.llm_service.summarize_content(content)
            note.summary = summary if summary else None

            # Generate and assign tags
            existing_tags = [tag.name for tag in Tag.query.all()]
            generated_tags = self.llm_service.generate_tags(content, existing_tags)

            for tag_name in generated_tags:
                tag = Tag.query.filter_by(name=tag_name).first()
                if not tag:
                    tag = Tag(name=tag_name)
                    db.session.add(tag)

                if tag not in note.tags:
                    note.tags.append(tag)

        except Exception as e:
            self.logger.error(f"Error processing with LLM: {e}", exc_info=True)