# Performance Optimization Guide

This guide covers performance optimization strategies for AIrchivist, including current optimizations and future improvement opportunities.

## Current Performance Features

### Backend Optimizations

#### Rate Limiting
- **API Rate Limits**: 120 requests/minute, 2000 requests/hour
- **Sliding Window**: Prevents burst traffic and ensures fair usage
- **Headers**: Rate limit information in response headers

#### Service Layer
- **Dependency Injection**: Singleton services reduce object creation overhead
- **Retry Logic**: Exponential backoff for LLM calls reduces unnecessary retries
- **Connection Pooling**: SQLAlchemy connection pooling for database efficiency

#### Response Optimization
- **Pagination**: Limits data transfer and processing time
- **Standardized Responses**: Consistent format reduces parsing overhead
- **Selective Loading**: Only load required fields for list views

### Frontend Optimizations

#### Loading States
- **Skeleton Loading**: Improves perceived performance
- **Progressive Loading**: Load critical content first
- **Error Boundaries**: Prevent cascading failures

#### State Management
- **React Context**: Efficient state updates without prop drilling
- **Memoization**: Prevent unnecessary re-renders
- **Lazy Loading**: Load components only when needed

## Performance Monitoring

### Current Metrics

#### Backend Metrics
```python
# Example performance logging
import time
from functools import wraps

def performance_monitor(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        duration = time.time() - start_time
        logger.info(f"{func.__name__} took {duration:.3f}s")
        return result
    return wrapper
```

#### Frontend Metrics
```javascript
// Performance monitoring hook
export function usePerformanceMonitor(componentName) {
  useEffect(() => {
    const startTime = performance.now();
    return () => {
      const duration = performance.now() - startTime;
      console.log(`${componentName} render time: ${duration.toFixed(2)}ms`);
    };
  }, [componentName]);
}
```

### Key Performance Indicators

- **API Response Time**: < 200ms for 95% of requests
- **Search Response Time**: < 100ms
- **Page Load Time**: < 2 seconds
- **Time to Interactive**: < 3 seconds
- **Memory Usage**: < 100MB for 1000 notes

## Database Performance

### Current Optimizations

#### SQLAlchemy Configuration
```python
# Optimized database configuration
SQLALCHEMY_ENGINE_OPTIONS = {
    'pool_size': 10,
    'pool_recycle': 3600,
    'pool_pre_ping': True,
    'echo': False  # Disable in production
}
```

#### Query Optimization
```python
# Efficient note loading with relationships
def get_notes_optimized(page, per_page):
    return Note.query.options(
        joinedload(Note.tags),
        selectinload(Note.links)
    ).paginate(
        page=page,
        per_page=per_page,
        error_out=False
    )
```

### Future Database Optimizations

#### Indexing Strategy
```sql
-- Recommended indexes for performance
CREATE INDEX idx_notes_title ON notes(title);
CREATE INDEX idx_notes_created_at ON notes(created_at);
CREATE INDEX idx_notes_updated_at ON notes(updated_at);
CREATE INDEX idx_notes_content_fts ON notes USING gin(to_tsvector('english', content));
CREATE INDEX idx_tags_name ON tags(name);
CREATE INDEX idx_note_tags_note_id ON note_tags(note_id);
CREATE INDEX idx_note_tags_tag_id ON note_tags(tag_id);
```

#### Query Optimization
```python
# Batch loading for better performance
def get_notes_with_tags_batch(note_ids):
    """Load multiple notes with their tags in a single query"""
    return db.session.query(Note).options(
        joinedload(Note.tags)
    ).filter(Note.id.in_(note_ids)).all()

# Pagination with cursor-based approach for large datasets
def get_notes_cursor_paginated(cursor=None, limit=20):
    """Cursor-based pagination for better performance on large datasets"""
    query = Note.query.order_by(Note.id)
    if cursor:
        query = query.filter(Note.id > cursor)
    return query.limit(limit).all()
```

## Search Performance

### Current Implementation
```python
# Basic search with LIKE queries
def search_notes(query):
    search_term = f"%{query}%"
    return Note.query.filter(
        or_(
            Note.title.ilike(search_term),
            Note.content.ilike(search_term)
        )
    ).all()
```

### Optimization Opportunities

#### Full-Text Search
```python
# PostgreSQL full-text search implementation
def search_notes_fts(query):
    """Full-text search with ranking"""
    return db.session.query(
        Note,
        func.ts_rank(
            func.to_tsvector('english', Note.content),
            func.plainto_tsquery('english', query)
        ).label('rank')
    ).filter(
        func.to_tsvector('english', Note.content).match(query)
    ).order_by(desc('rank')).all()
```

#### Search Indexing
```python
# Whoosh-based search index
from whoosh.index import create_index
from whoosh.fields import Schema, TEXT, ID

# Define search schema
schema = Schema(
    id=ID(stored=True),
    title=TEXT(stored=True),
    content=TEXT(stored=True),
    tags=TEXT(stored=True)
)

def build_search_index():
    """Build search index for fast querying"""
    ix = create_index(schema)
    writer = ix.writer()
    
    for note in Note.query.all():
        writer.add_document(
            id=str(note.id),
            title=note.title,
            content=note.content,
            tags=' '.join(tag.name for tag in note.tags)
        )
    
    writer.commit()
    return ix
```

## Caching Strategies

### Application-Level Caching
```python
from functools import lru_cache
from flask_caching import Cache

# Initialize cache
cache = Cache(app, config={'CACHE_TYPE': 'simple'})

# Cache expensive operations
@cache.memoize(timeout=300)
def get_popular_tags():
    """Cache popular tags for 5 minutes"""
    return db.session.query(
        Tag.name,
        func.count(note_tags.c.note_id).label('count')
    ).join(note_tags).group_by(Tag.name).order_by(desc('count')).limit(10).all()

# LRU cache for frequently accessed data
@lru_cache(maxsize=128)
def get_note_summary(note_id):
    """Cache note summaries in memory"""
    note = Note.query.get(note_id)
    return note.summary if note else None
```

### Redis Caching
```python
import redis
import json

redis_client = redis.Redis(host='localhost', port=6379, db=0)

def cache_search_results(query, results, ttl=300):
    """Cache search results in Redis"""
    cache_key = f"search:{hash(query)}"
    redis_client.setex(
        cache_key,
        ttl,
        json.dumps([note.to_dict() for note in results])
    )

def get_cached_search_results(query):
    """Retrieve cached search results"""
    cache_key = f"search:{hash(query)}"
    cached = redis_client.get(cache_key)
    return json.loads(cached) if cached else None
```

## Frontend Performance

### React Optimizations

#### Component Memoization
```javascript
import React, { memo, useMemo, useCallback } from 'react';

// Memoize expensive components
const NoteItem = memo(({ note, onSelect }) => {
  const formattedDate = useMemo(() => 
    new Date(note.updated_at).toLocaleDateString(),
    [note.updated_at]
  );

  const handleClick = useCallback(() => 
    onSelect(note.id),
    [note.id, onSelect]
  );

  return (
    <div onClick={handleClick}>
      <h3>{note.title}</h3>
      <p>{formattedDate}</p>
    </div>
  );
});
```

#### Virtual Scrolling
```javascript
import { FixedSizeList as List } from 'react-window';

function VirtualizedNoteList({ notes }) {
  const Row = ({ index, style }) => (
    <div style={style}>
      <NoteItem note={notes[index]} />
    </div>
  );

  return (
    <List
      height={600}
      itemCount={notes.length}
      itemSize={100}
      width="100%"
    >
      {Row}
    </List>
  );
}
```

#### Lazy Loading
```javascript
import { lazy, Suspense } from 'react';

// Lazy load heavy components
const GraphView = lazy(() => import('./GraphView'));
const ImportDialog = lazy(() => import('./ImportDialog'));

function App() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <GraphView />
    </Suspense>
  );
}
```

### Bundle Optimization

#### Code Splitting
```javascript
// vite.config.js
export default {
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          graph: ['d3', 'force-graph'],
          ui: ['lucide-react']
        }
      }
    }
  }
};
```

## Monitoring and Profiling

### Backend Profiling
```python
import cProfile
import pstats
from functools import wraps

def profile_endpoint(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        profiler = cProfile.Profile()
        profiler.enable()
        result = func(*args, **kwargs)
        profiler.disable()
        
        stats = pstats.Stats(profiler)
        stats.sort_stats('cumulative')
        stats.print_stats(10)  # Top 10 functions
        
        return result
    return wrapper
```

### Frontend Profiling
```javascript
// Performance measurement
function measurePerformance(name, fn) {
  return async (...args) => {
    const start = performance.now();
    const result = await fn(...args);
    const end = performance.now();
    console.log(`${name} took ${end - start} milliseconds`);
    return result;
  };
}

// Memory usage monitoring
function monitorMemoryUsage() {
  if (performance.memory) {
    console.log({
      used: Math.round(performance.memory.usedJSHeapSize / 1048576),
      total: Math.round(performance.memory.totalJSHeapSize / 1048576),
      limit: Math.round(performance.memory.jsHeapSizeLimit / 1048576)
    });
  }
}
```

## Performance Testing

### Load Testing
```python
# locustfile.py for load testing
from locust import HttpUser, task, between

class AIrchivistUser(HttpUser):
    wait_time = between(1, 3)
    
    @task(3)
    def get_notes(self):
        self.client.get("/api/notes")
    
    @task(2)
    def search_notes(self):
        self.client.get("/api/notes?search=python")
    
    @task(1)
    def get_note_detail(self):
        self.client.get("/api/notes/1")
```

### Performance Benchmarks
```bash
# Run load tests
locust -f locustfile.py --host=http://localhost:5000

# Database performance testing
python -m pytest tests/performance/ -v

# Frontend performance testing
npm run test:performance
```

## Best Practices

### General Guidelines
1. **Measure First**: Always profile before optimizing
2. **Optimize Bottlenecks**: Focus on the slowest parts
3. **Cache Wisely**: Cache expensive operations, not everything
4. **Monitor Continuously**: Set up performance monitoring
5. **Test Regularly**: Include performance tests in CI/CD

### Database Best Practices
1. **Use Indexes**: Add indexes for frequently queried columns
2. **Limit Results**: Always use pagination for large datasets
3. **Optimize Queries**: Use EXPLAIN to analyze query performance
4. **Connection Pooling**: Configure appropriate pool sizes
5. **Avoid N+1**: Use eager loading for relationships

### Frontend Best Practices
1. **Code Splitting**: Split bundles for faster loading
2. **Lazy Loading**: Load components when needed
3. **Memoization**: Prevent unnecessary re-renders
4. **Virtual Scrolling**: Handle large lists efficiently
5. **Image Optimization**: Optimize and compress images

This performance guide will help maintain and improve AIrchivist's performance as it scales.
