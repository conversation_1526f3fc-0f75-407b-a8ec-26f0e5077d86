"""
Standardized API response formatting utilities
"""
from datetime import datetime
from typing import Any, Dict, List, Optional, Union
from flask import jsonify, Response

from src.utils.logging_config import get_logger

logger = get_logger('airchivist.response_formatter')


class APIResponse:
    """
    Standardized API response format
    """
    
    def __init__(self, success: bool = True, data: Any = None, message: str = None, 
                 errors: List[str] = None, meta: Dict[str, Any] = None):
        self.success = success
        self.data = data
        self.message = message
        self.errors = errors or []
        self.meta = meta or {}
        self.timestamp = datetime.utcnow().isoformat()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        response = {
            'success': self.success,
            'timestamp': self.timestamp
        }
        
        if self.data is not None:
            response['data'] = self.data
        
        if self.message:
            response['message'] = self.message
        
        if self.errors:
            response['errors'] = self.errors
        
        if self.meta:
            response['meta'] = self.meta
        
        return response
    
    def to_response(self, status_code: int = 200) -> Response:
        """Convert to Flask response"""
        return jsonify(self.to_dict()), status_code


class PaginationMeta:
    """
    Pagination metadata
    """
    
    def __init__(self, page: int, per_page: int, total: int, pages: int,
                 has_prev: bool, has_next: bool, prev_num: Optional[int] = None,
                 next_num: Optional[int] = None):
        self.page = page
        self.per_page = per_page
        self.total = total
        self.pages = pages
        self.has_prev = has_prev
        self.has_next = has_next
        self.prev_num = prev_num
        self.next_num = next_num
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            'pagination': {
                'page': self.page,
                'per_page': self.per_page,
                'total': self.total,
                'pages': self.pages,
                'has_prev': self.has_prev,
                'has_next': self.has_next,
                'prev_num': self.prev_num,
                'next_num': self.next_num
            }
        }


def success_response(data: Any = None, message: str = None, 
                    meta: Dict[str, Any] = None, status_code: int = 200) -> Response:
    """
    Create a success response
    
    Args:
        data: Response data
        message: Success message
        meta: Additional metadata
        status_code: HTTP status code
        
    Returns:
        Flask response
    """
    response = APIResponse(success=True, data=data, message=message, meta=meta)
    return response.to_response(status_code)


def error_response(message: str, errors: List[str] = None, 
                  status_code: int = 400, meta: Dict[str, Any] = None) -> Response:
    """
    Create an error response
    
    Args:
        message: Error message
        errors: List of specific errors
        status_code: HTTP status code
        meta: Additional metadata
        
    Returns:
        Flask response
    """
    response = APIResponse(
        success=False, 
        message=message, 
        errors=errors or [message],
        meta=meta
    )
    return response.to_response(status_code)


def paginated_response(items: List[Any], pagination_meta: PaginationMeta,
                      message: str = None, additional_meta: Dict[str, Any] = None) -> Response:
    """
    Create a paginated response
    
    Args:
        items: List of items for current page
        pagination_meta: Pagination metadata
        message: Optional message
        additional_meta: Additional metadata
        
    Returns:
        Flask response
    """
    meta = pagination_meta.to_dict()
    if additional_meta:
        meta.update(additional_meta)
    
    response = APIResponse(
        success=True,
        data=items,
        message=message,
        meta=meta
    )
    return response.to_response(200)


def validation_error_response(field: str, message: str) -> Response:
    """
    Create a validation error response
    
    Args:
        field: Field that failed validation
        message: Validation error message
        
    Returns:
        Flask response
    """
    return error_response(
        message="Validation failed",
        errors=[f"{field}: {message}"],
        status_code=422,
        meta={'field': field}
    )


def not_found_response(resource: str = "Resource") -> Response:
    """
    Create a not found response
    
    Args:
        resource: Name of the resource that wasn't found
        
    Returns:
        Flask response
    """
    return error_response(
        message=f"{resource} not found",
        status_code=404
    )


def internal_error_response(message: str = "Internal server error") -> Response:
    """
    Create an internal server error response
    
    Args:
        message: Error message
        
    Returns:
        Flask response
    """
    return error_response(
        message=message,
        status_code=500
    )


def created_response(data: Any, message: str = "Resource created successfully") -> Response:
    """
    Create a resource created response
    
    Args:
        data: Created resource data
        message: Success message
        
    Returns:
        Flask response
    """
    return success_response(data=data, message=message, status_code=201)


def updated_response(data: Any, message: str = "Resource updated successfully") -> Response:
    """
    Create a resource updated response
    
    Args:
        data: Updated resource data
        message: Success message
        
    Returns:
        Flask response
    """
    return success_response(data=data, message=message, status_code=200)


def deleted_response(message: str = "Resource deleted successfully") -> Response:
    """
    Create a resource deleted response
    
    Args:
        message: Success message
        
    Returns:
        Flask response
    """
    return success_response(message=message, status_code=200)


def no_content_response() -> Response:
    """
    Create a no content response
    
    Returns:
        Flask response with 204 status
    """
    return '', 204
