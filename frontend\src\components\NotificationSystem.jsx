/**
 * Notification system for user feedback
 */
import React, { createContext, useContext, useReducer, useCallback } from 'react';
import { X, CheckCircle, AlertCircle, AlertTriangle, Info } from 'lucide-react';

// Notification types
export const NotificationTypes = {
  SUCCESS: 'success',
  ERROR: 'error',
  WARNING: 'warning',
  INFO: 'info',
};

// Action types
const ActionTypes = {
  ADD_NOTIFICATION: 'ADD_NOTIFICATION',
  REMOVE_NOTIFICATION: 'REMOVE_NOTIFICATION',
  CLEAR_ALL: 'CLEAR_ALL',
};

// Initial state
const initialState = {
  notifications: [],
};

// Reducer
function notificationReducer(state, action) {
  switch (action.type) {
    case ActionTypes.ADD_NOTIFICATION:
      return {
        ...state,
        notifications: [...state.notifications, action.payload],
      };

    case ActionTypes.REMOVE_NOTIFICATION:
      return {
        ...state,
        notifications: state.notifications.filter(
          notification => notification.id !== action.payload
        ),
      };

    case ActionTypes.CLEAR_ALL:
      return {
        ...state,
        notifications: [],
      };

    default:
      return state;
  }
}

// Context
const NotificationContext = createContext();

// Provider component
export function NotificationProvider({ children }) {
  const [state, dispatch] = useReducer(notificationReducer, initialState);

  const addNotification = useCallback((notification) => {
    const id = Date.now() + Math.random();
    const newNotification = {
      id,
      type: NotificationTypes.INFO,
      duration: 5000,
      ...notification,
    };

    dispatch({
      type: ActionTypes.ADD_NOTIFICATION,
      payload: newNotification,
    });

    // Auto-remove notification after duration
    if (newNotification.duration > 0) {
      setTimeout(() => {
        dispatch({
          type: ActionTypes.REMOVE_NOTIFICATION,
          payload: id,
        });
      }, newNotification.duration);
    }

    return id;
  }, []);

  const removeNotification = useCallback((id) => {
    dispatch({
      type: ActionTypes.REMOVE_NOTIFICATION,
      payload: id,
    });
  }, []);

  const clearAll = useCallback(() => {
    dispatch({ type: ActionTypes.CLEAR_ALL });
  }, []);

  // Convenience methods
  const success = useCallback((message, options = {}) => {
    return addNotification({
      type: NotificationTypes.SUCCESS,
      message,
      ...options,
    });
  }, [addNotification]);

  const error = useCallback((message, options = {}) => {
    return addNotification({
      type: NotificationTypes.ERROR,
      message,
      duration: 0, // Don't auto-remove error notifications
      ...options,
    });
  }, [addNotification]);

  const warning = useCallback((message, options = {}) => {
    return addNotification({
      type: NotificationTypes.WARNING,
      message,
      duration: 7000,
      ...options,
    });
  }, [addNotification]);

  const info = useCallback((message, options = {}) => {
    return addNotification({
      type: NotificationTypes.INFO,
      message,
      ...options,
    });
  }, [addNotification]);

  const value = {
    notifications: state.notifications,
    addNotification,
    removeNotification,
    clearAll,
    success,
    error,
    warning,
    info,
  };

  return (
    <NotificationContext.Provider value={value}>
      {children}
      <NotificationContainer />
    </NotificationContext.Provider>
  );
}

// Hook to use notifications
export function useNotifications() {
  const context = useContext(NotificationContext);
  if (!context) {
    throw new Error('useNotifications must be used within a NotificationProvider');
  }
  return context;
}

// Individual notification component
function NotificationItem({ notification, onRemove }) {
  const { id, type, message, title, action } = notification;

  const icons = {
    [NotificationTypes.SUCCESS]: CheckCircle,
    [NotificationTypes.ERROR]: AlertCircle,
    [NotificationTypes.WARNING]: AlertTriangle,
    [NotificationTypes.INFO]: Info,
  };

  const colors = {
    [NotificationTypes.SUCCESS]: 'bg-green-50 border-green-200 text-green-800',
    [NotificationTypes.ERROR]: 'bg-red-50 border-red-200 text-red-800',
    [NotificationTypes.WARNING]: 'bg-yellow-50 border-yellow-200 text-yellow-800',
    [NotificationTypes.INFO]: 'bg-blue-50 border-blue-200 text-blue-800',
  };

  const iconColors = {
    [NotificationTypes.SUCCESS]: 'text-green-400',
    [NotificationTypes.ERROR]: 'text-red-400',
    [NotificationTypes.WARNING]: 'text-yellow-400',
    [NotificationTypes.INFO]: 'text-blue-400',
  };

  const Icon = icons[type];

  return (
    <div className={`max-w-sm w-full border rounded-lg shadow-lg p-4 ${colors[type]}`}>
      <div className="flex items-start">
        <div className="flex-shrink-0">
          <Icon className={`w-5 h-5 ${iconColors[type]}`} />
        </div>
        
        <div className="ml-3 w-0 flex-1">
          {title && (
            <p className="text-sm font-medium">
              {title}
            </p>
          )}
          <p className={`text-sm ${title ? 'mt-1' : ''}`}>
            {message}
          </p>
          
          {action && (
            <div className="mt-3">
              {action}
            </div>
          )}
        </div>
        
        <div className="ml-4 flex-shrink-0 flex">
          <button
            onClick={() => onRemove(id)}
            className="inline-flex text-gray-400 hover:text-gray-600 focus:outline-none focus:text-gray-600 transition-colors"
          >
            <X className="w-4 h-4" />
          </button>
        </div>
      </div>
    </div>
  );
}

// Container component that renders all notifications
function NotificationContainer() {
  const { notifications, removeNotification } = useNotifications();

  if (notifications.length === 0) {
    return null;
  }

  return (
    <div className="fixed top-4 right-4 z-50 space-y-2">
      {notifications.map((notification) => (
        <NotificationItem
          key={notification.id}
          notification={notification}
          onRemove={removeNotification}
        />
      ))}
    </div>
  );
}

// Hook for API error notifications
export function useAPINotifications() {
  const { success, error, warning } = useNotifications();

  const notifySuccess = useCallback((message, options = {}) => {
    return success(message, options);
  }, [success]);

  const notifyError = useCallback((err, options = {}) => {
    let message = 'An unexpected error occurred';
    
    if (typeof err === 'string') {
      message = err;
    } else if (err?.message) {
      message = err.message;
    }

    return error(message, {
      title: 'Error',
      ...options,
    });
  }, [error]);

  const notifyAPIError = useCallback((apiError, options = {}) => {
    let message = 'An unexpected error occurred';
    let title = 'Error';

    if (apiError?.status === 429) {
      title = 'Rate Limited';
      message = apiError.message || 'Too many requests. Please try again later.';
    } else if (apiError?.status >= 500) {
      title = 'Server Error';
      message = 'Server is experiencing issues. Please try again later.';
    } else if (apiError?.status === 404) {
      title = 'Not Found';
      message = 'The requested resource was not found.';
    } else if (apiError?.message) {
      message = apiError.message;
    }

    return error(message, {
      title,
      ...options,
    });
  }, [error]);

  const notifyValidationError = useCallback((validationError, options = {}) => {
    const message = validationError?.message || 'Please check your input and try again.';
    
    return warning(message, {
      title: 'Validation Error',
      ...options,
    });
  }, [warning]);

  return {
    notifySuccess,
    notifyError,
    notifyAPIError,
    notifyValidationError,
  };
}

export default NotificationProvider;
