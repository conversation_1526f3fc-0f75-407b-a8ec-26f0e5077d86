/**
 * Centralized API service for making HTTP requests
 */

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:5000/api';

class APIError extends Error {
  constructor(message, status, data = null) {
    super(message);
    this.name = 'APIError';
    this.status = status;
    this.data = data;
  }
}

class APIService {
  constructor(baseURL = API_BASE_URL) {
    this.baseURL = baseURL;
    this.defaultHeaders = {
      'Content-Type': 'application/json',
    };
  }

  /**
   * Make HTTP request with error handling
   */
  async request(endpoint, options = {}) {
    const url = `${this.baseURL}${endpoint}`;
    const config = {
      headers: { ...this.defaultHeaders, ...options.headers },
      ...options,
    };

    try {
      const response = await fetch(url, config);
      
      // Handle rate limiting
      if (response.status === 429) {
        const retryAfter = response.headers.get('Retry-After') || '60';
        throw new APIError(
          `Rate limit exceeded. Please try again in ${retryAfter} seconds.`,
          429,
          { retryAfter: parseInt(retryAfter) }
        );
      }

      // Parse response
      let data = null;
      const contentType = response.headers.get('content-type');
      if (contentType && contentType.includes('application/json')) {
        data = await response.json();
      } else {
        data = await response.text();
      }

      // Handle error responses
      if (!response.ok) {
        const message = data?.message || data?.error || `HTTP ${response.status}`;
        throw new APIError(message, response.status, data);
      }

      return data;
    } catch (error) {
      if (error instanceof APIError) {
        throw error;
      }
      
      // Network or other errors
      throw new APIError(
        error.message || 'Network error occurred',
        0,
        { originalError: error }
      );
    }
  }

  /**
   * GET request
   */
  async get(endpoint, params = {}) {
    const searchParams = new URLSearchParams(params);
    const url = searchParams.toString() ? `${endpoint}?${searchParams}` : endpoint;
    return this.request(url, { method: 'GET' });
  }

  /**
   * POST request
   */
  async post(endpoint, data = null) {
    return this.request(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : null,
    });
  }

  /**
   * PUT request
   */
  async put(endpoint, data = null) {
    return this.request(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : null,
    });
  }

  /**
   * DELETE request
   */
  async delete(endpoint) {
    return this.request(endpoint, { method: 'DELETE' });
  }

  /**
   * Upload file
   */
  async upload(endpoint, file, additionalData = {}) {
    const formData = new FormData();
    formData.append('file', file);
    
    Object.keys(additionalData).forEach(key => {
      formData.append(key, additionalData[key]);
    });

    return this.request(endpoint, {
      method: 'POST',
      body: formData,
      headers: {}, // Let browser set Content-Type for FormData
    });
  }
}

// Create singleton instance
const apiService = new APIService();

/**
 * Notes API methods
 */
export const notesAPI = {
  /**
   * Get all notes with pagination and filtering
   */
  async getNotes(params = {}) {
    return apiService.get('/notes', params);
  },

  /**
   * Get a specific note by ID
   */
  async getNote(id) {
    return apiService.get(`/notes/${id}`);
  },

  /**
   * Update note content
   */
  async updateNote(id, data) {
    return apiService.put(`/notes/${id}`, data);
  },

  /**
   * Delete a note
   */
  async deleteNote(id) {
    return apiService.delete(`/notes/${id}`);
  },

  /**
   * Import files
   */
  async importFiles(data) {
    return apiService.post('/notes/import', data);
  },

  /**
   * Get graph data
   */
  async getGraphData() {
    return apiService.get('/notes/graph');
  },

  /**
   * Process note with LLM
   */
  async processNoteWithLLM(id) {
    return apiService.post(`/llm/process/${id}`);
  },

  /**
   * Test LLM connection
   */
  async testLLMConnection() {
    return apiService.get('/llm/test');
  },

  /**
   * Get/Set notes directory
   */
  async getNotesDirectory() {
    return apiService.get('/config/notes-directory');
  },

  async setNotesDirectory(directory) {
    return apiService.post('/config/notes-directory', { directory });
  },
};

/**
 * Tags API methods
 */
export const tagsAPI = {
  /**
   * Get all tags
   */
  async getTags() {
    return apiService.get('/tags');
  },

  /**
   * Update tag
   */
  async updateTag(id, data) {
    return apiService.put(`/tags/${id}`, data);
  },

  /**
   * Delete tag
   */
  async deleteTag(id) {
    return apiService.delete(`/tags/${id}`);
  },
};

/**
 * Links API methods
 */
export const linksAPI = {
  /**
   * Get all links
   */
  async getLinks() {
    return apiService.get('/links');
  },

  /**
   * Create new link
   */
  async createLink(data) {
    return apiService.post('/links', data);
  },

  /**
   * Update link
   */
  async updateLink(id, data) {
    return apiService.put(`/links/${id}`, data);
  },

  /**
   * Delete link
   */
  async deleteLink(id) {
    return apiService.delete(`/links/${id}`);
  },

  /**
   * Get links for a specific note
   */
  async getNoteLinks(noteId) {
    return apiService.get(`/notes/${noteId}/links`);
  },
};

// Export the main API service and error class
export { apiService, APIError };
export default apiService;
