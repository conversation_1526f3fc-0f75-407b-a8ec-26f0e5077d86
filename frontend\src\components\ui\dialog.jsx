import React, { useState } from 'react'

export const Dialog = ({ children, open, onOpenChange }) => {
  return (
    <div className="dialog-root">
      {React.Children.map(children, child =>
        React.cloneElement(child, { open, onOpenChange })
      )}
    </div>
  )
}

export const DialogTrigger = ({ children, open, onOpenChange }) => {
  return React.cloneElement(children, {
    onClick: () => onOpenChange?.(true)
  })
}

export const DialogContent = ({ children, open, onOpenChange, className = '' }) => {
  if (!open) return null
  
  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      <div 
        className="fixed inset-0 bg-background/80 backdrop-blur-sm"
        onClick={() => onOpenChange?.(false)}
      />
      <div className={`relative z-50 grid w-full max-w-lg gap-4 border bg-background p-6 shadow-lg duration-200 sm:rounded-lg ${className}`}>
        {children}
      </div>
    </div>
  )
}

export const DialogHeader = ({ children, className = '' }) => {
  return (
    <div className={`flex flex-col space-y-1.5 text-center sm:text-left ${className}`}>
      {children}
    </div>
  )
}

export const DialogTitle = ({ children, className = '' }) => {
  return (
    <h2 className={`text-lg font-semibold leading-none tracking-tight ${className}`}>
      {children}
    </h2>
  )
}

export const DialogDescription = ({ children, className = '' }) => {
  return (
    <p className={`text-sm text-muted-foreground ${className}`}>
      {children}
    </p>
  )
}
