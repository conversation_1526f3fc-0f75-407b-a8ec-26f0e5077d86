"""
Input validation utilities for API endpoints
"""
import os
import re
from typing import Any, Dict, List, Optional, Union
from functools import wraps
from flask import request, jsonify

from src.config import get_config
from src.utils.logging_config import get_logger, create_error_response

logger = get_logger('airchivist.validation')
config = get_config()


class ValidationError(Exception):
    """Custom validation error"""
    def __init__(self, message: str, field: str = None):
        self.message = message
        self.field = field
        super().__init__(message)


def validate_required_fields(data: Dict[str, Any], required_fields: List[str]) -> None:
    """
    Validate that all required fields are present in data
    
    Args:
        data: Data dictionary to validate
        required_fields: List of required field names
        
    Raises:
        ValidationError: If any required field is missing
    """
    if not data:
        raise ValidationError("Request data is required")
    
    missing_fields = [field for field in required_fields if field not in data or data[field] is None]
    if missing_fields:
        raise ValidationError(f"Missing required fields: {', '.join(missing_fields)}")


def validate_string_field(value: Any, field_name: str, min_length: int = 1, max_length: int = None, 
                         pattern: str = None, allow_empty: bool = False) -> str:
    """
    Validate a string field
    
    Args:
        value: Value to validate
        field_name: Name of the field for error messages
        min_length: Minimum length (default: 1)
        max_length: Maximum length (optional)
        pattern: Regex pattern to match (optional)
        allow_empty: Whether to allow empty strings
        
    Returns:
        Validated string value
        
    Raises:
        ValidationError: If validation fails
    """
    if value is None:
        raise ValidationError(f"{field_name} is required", field_name)
    
    if not isinstance(value, str):
        raise ValidationError(f"{field_name} must be a string", field_name)
    
    if not allow_empty and len(value.strip()) == 0:
        raise ValidationError(f"{field_name} cannot be empty", field_name)
    
    if len(value) < min_length:
        raise ValidationError(f"{field_name} must be at least {min_length} characters long", field_name)
    
    if max_length and len(value) > max_length:
        raise ValidationError(f"{field_name} must be no more than {max_length} characters long", field_name)
    
    if pattern and not re.match(pattern, value):
        raise ValidationError(f"{field_name} format is invalid", field_name)
    
    return value.strip()


def validate_integer_field(value: Any, field_name: str, min_value: int = None, max_value: int = None) -> int:
    """
    Validate an integer field
    
    Args:
        value: Value to validate
        field_name: Name of the field for error messages
        min_value: Minimum value (optional)
        max_value: Maximum value (optional)
        
    Returns:
        Validated integer value
        
    Raises:
        ValidationError: If validation fails
    """
    if value is None:
        raise ValidationError(f"{field_name} is required", field_name)
    
    try:
        int_value = int(value)
    except (ValueError, TypeError):
        raise ValidationError(f"{field_name} must be an integer", field_name)
    
    if min_value is not None and int_value < min_value:
        raise ValidationError(f"{field_name} must be at least {min_value}", field_name)
    
    if max_value is not None and int_value > max_value:
        raise ValidationError(f"{field_name} must be no more than {max_value}", field_name)
    
    return int_value


def validate_boolean_field(value: Any, field_name: str) -> bool:
    """
    Validate a boolean field
    
    Args:
        value: Value to validate
        field_name: Name of the field for error messages
        
    Returns:
        Validated boolean value
        
    Raises:
        ValidationError: If validation fails
    """
    if value is None:
        raise ValidationError(f"{field_name} is required", field_name)
    
    if isinstance(value, bool):
        return value
    
    if isinstance(value, str):
        if value.lower() in ('true', '1', 'yes', 'on'):
            return True
        elif value.lower() in ('false', '0', 'no', 'off'):
            return False
    
    raise ValidationError(f"{field_name} must be a boolean value", field_name)


def validate_file_path(file_path: str, field_name: str = "file_path") -> str:
    """
    Validate a file path
    
    Args:
        file_path: File path to validate
        field_name: Name of the field for error messages
        
    Returns:
        Validated file path
        
    Raises:
        ValidationError: If validation fails
    """
    validated_path = validate_string_field(file_path, field_name, max_length=500)
    
    if not os.path.exists(validated_path):
        raise ValidationError(f"File not found: {validated_path}", field_name)
    
    if not os.path.isfile(validated_path):
        raise ValidationError(f"Path is not a file: {validated_path}", field_name)
    
    # Check file extension
    file_ext = os.path.splitext(validated_path)[1].lower()
    if file_ext not in config.SUPPORTED_FILE_TYPES:
        raise ValidationError(
            f"Unsupported file type: {file_ext}. Supported types: {', '.join(config.SUPPORTED_FILE_TYPES)}", 
            field_name
        )
    
    # Check file size
    file_size_mb = os.path.getsize(validated_path) / (1024 * 1024)
    if file_size_mb > config.MAX_FILE_SIZE_MB:
        raise ValidationError(
            f"File too large: {file_size_mb:.1f}MB. Maximum size: {config.MAX_FILE_SIZE_MB}MB", 
            field_name
        )
    
    return validated_path


def validate_directory_path(dir_path: str, field_name: str = "directory_path") -> str:
    """
    Validate a directory path
    
    Args:
        dir_path: Directory path to validate
        field_name: Name of the field for error messages
        
    Returns:
        Validated directory path
        
    Raises:
        ValidationError: If validation fails
    """
    validated_path = validate_string_field(dir_path, field_name, max_length=500)
    
    if not os.path.exists(validated_path):
        raise ValidationError(f"Directory not found: {validated_path}", field_name)
    
    if not os.path.isdir(validated_path):
        raise ValidationError(f"Path is not a directory: {validated_path}", field_name)
    
    return validated_path


def validate_pagination_params(page: Any = None, per_page: Any = None) -> tuple[int, int]:
    """
    Validate pagination parameters
    
    Args:
        page: Page number
        per_page: Items per page
        
    Returns:
        Tuple of (page, per_page) validated values
        
    Raises:
        ValidationError: If validation fails
    """
    if page is not None:
        page = validate_integer_field(page, "page", min_value=1)
    else:
        page = 1
    
    if per_page is not None:
        per_page = validate_integer_field(per_page, "per_page", min_value=1, max_value=config.MAX_PAGE_SIZE)
    else:
        per_page = config.DEFAULT_PAGE_SIZE
    
    return page, per_page


def validate_json_request(required_fields: List[str] = None):
    """
    Decorator to validate JSON request data
    
    Args:
        required_fields: List of required field names
        
    Returns:
        Decorator function
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            try:
                data = request.get_json()
                if required_fields:
                    validate_required_fields(data, required_fields)
                return f(*args, **kwargs)
            except ValidationError as e:
                logger.warning(f"Validation error in {f.__name__}: {e.message}")
                error_response = create_error_response(e.message, 400, {'field': e.field} if e.field else None)
                return jsonify(error_response), 400
            except Exception as e:
                logger.error(f"Unexpected error in {f.__name__}: {e}", exc_info=True)
                error_response = create_error_response("Internal server error", 500)
                return jsonify(error_response), 500
        return decorated_function
    return decorator
