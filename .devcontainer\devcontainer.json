{"name": "AIrchivist Development Environment", "build": {"dockerfile": "Dockerfile", "context": ".."}, "features": {"ghcr.io/devcontainers/features/git:1": {}, "ghcr.io/devcontainers/features/github-cli:1": {}, "ghcr.io/devcontainers/features/node:1": {"version": "lts"}, "ghcr.io/devcontainers/features/python:1": {"version": "3"}}, "customizations": {"vscode": {"extensions": ["ms-python.python", "ms-python.vscode-pylance", "ms-toolsai.jupyter", "ms-vscode.vscode-typescript-next", "dbaeumer.vscode-eslint", "esbenp.prettier-vscode"]}}, "remoteUser": "vscode"}