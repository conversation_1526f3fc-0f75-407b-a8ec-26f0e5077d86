# Contributing to <PERSON><PERSON><PERSON>

Thank you for your interest in contributing to <PERSON>rch<PERSON>! This guide will help you get started with contributing to the project.

## 🚀 Quick Start

1. **Fork the repository** on GitHub
2. **Clone your fork** locally
3. **Set up the development environment** (see below)
4. **Create a feature branch** for your changes
5. **Make your changes** with tests
6. **Submit a pull request**

## 🛠️ Development Setup

### Prerequisites

- Python 3.8+ (backend)
- Node.js 16+ (frontend)
- Git
- Ollama (for LLM features)

### Backend Setup

```bash
cd backend

# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt
pip install -r requirements-dev.txt  # Development dependencies

# Set up pre-commit hooks
pre-commit install

# Run tests to verify setup
pytest
```

### Frontend Setup

```bash
cd frontend

# Install dependencies
npm install

# Run tests to verify setup
npm test

# Start development server
npm run dev
```

### Environment Configuration

Copy the example environment files and configure them:

```bash
# Backend
cp backend/.env.example backend/.env

# Frontend
cp frontend/.env.example frontend/.env.local
```

## 📋 Development Guidelines

### Code Style

#### Backend (Python)
- Follow PEP 8 style guidelines
- Use type hints where appropriate
- Maximum line length: 100 characters
- Use Black for code formatting
- Use isort for import sorting

```bash
# Format code
black src/
isort src/

# Check style
flake8 src/
mypy src/
```

#### Frontend (JavaScript/React)
- Use ESLint and Prettier for code formatting
- Follow React best practices
- Use functional components with hooks
- Prefer TypeScript for new components

```bash
# Format code
npm run format

# Check style
npm run lint
```

### Commit Messages

Follow the [Conventional Commits](https://www.conventionalcommits.org/) specification:

```
type(scope): description

[optional body]

[optional footer]
```

Types:
- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation changes
- `style`: Code style changes (formatting, etc.)
- `refactor`: Code refactoring
- `test`: Adding or updating tests
- `chore`: Maintenance tasks

Examples:
```
feat(api): add pagination to notes endpoint
fix(frontend): resolve memory leak in graph component
docs(readme): update installation instructions
test(services): add unit tests for tag service
```

### Branch Naming

Use descriptive branch names:
- `feature/add-search-filters`
- `fix/memory-leak-graph-view`
- `docs/api-documentation`
- `refactor/service-layer`

## 🧪 Testing

### Backend Testing

```bash
cd backend

# Run all tests
pytest

# Run with coverage
pytest --cov=src --cov-report=html

# Run specific test file
pytest tests/test_services.py

# Run specific test
pytest tests/test_services.py::TestNoteService::test_create_note
```

### Frontend Testing

```bash
cd frontend

# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage
```

### Test Guidelines

- Write tests for all new features
- Maintain test coverage above 80%
- Use descriptive test names
- Test both success and error cases
- Mock external dependencies

## 📝 Documentation

### API Documentation

When adding new API endpoints:

1. Update `docs/API.md` with the new endpoint
2. Include request/response examples
3. Document all parameters and error codes
4. Add rate limiting information if applicable

### Code Documentation

- Add docstrings to all functions and classes
- Use clear, descriptive variable names
- Comment complex logic
- Update README if adding new features

### Documentation Format

```python
def create_note(title: str, content: str, tags: List[str] = None) -> Note:
    """
    Create a new note with optional tags.
    
    Args:
        title: The note title
        content: The note content
        tags: Optional list of tag names
        
    Returns:
        The created note instance
        
    Raises:
        ValueError: If title is empty
        DatabaseError: If database operation fails
    """
```

## 🐛 Bug Reports

When reporting bugs, please include:

1. **Clear description** of the issue
2. **Steps to reproduce** the bug
3. **Expected behavior** vs actual behavior
4. **Environment information** (OS, Python/Node version, etc.)
5. **Error messages** or logs if available
6. **Screenshots** if applicable

Use the bug report template when creating issues.

## ✨ Feature Requests

When requesting features:

1. **Describe the problem** you're trying to solve
2. **Explain the proposed solution**
3. **Consider alternatives** you've thought of
4. **Provide use cases** and examples
5. **Check existing issues** to avoid duplicates

Use the feature request template when creating issues.

## 🔄 Pull Request Process

### Before Submitting

1. **Ensure tests pass** locally
2. **Update documentation** if needed
3. **Add tests** for new functionality
4. **Follow code style** guidelines
5. **Rebase** your branch on the latest main

### Pull Request Checklist

- [ ] Tests pass locally
- [ ] Code follows style guidelines
- [ ] Documentation updated
- [ ] Commit messages follow convention
- [ ] No merge conflicts
- [ ] PR description explains changes

### PR Description Template

```markdown
## Description
Brief description of changes

## Type of Change
- [ ] Bug fix
- [ ] New feature
- [ ] Breaking change
- [ ] Documentation update

## Testing
- [ ] Unit tests added/updated
- [ ] Integration tests added/updated
- [ ] Manual testing completed

## Screenshots (if applicable)

## Checklist
- [ ] Code follows style guidelines
- [ ] Self-review completed
- [ ] Documentation updated
- [ ] Tests added/updated
```

## 🏗️ Architecture Guidelines

### Backend Architecture

- Use the service layer pattern
- Implement proper dependency injection
- Follow SOLID principles
- Use interfaces for service contracts
- Handle errors gracefully with proper logging

### Frontend Architecture

- Use React functional components
- Implement error boundaries
- Use custom hooks for API calls
- Manage state with React Context
- Handle loading and error states

### Database Guidelines

- Use SQLAlchemy ORM
- Write migrations for schema changes
- Use proper indexing for performance
- Follow database naming conventions
- Implement proper relationships

## 🚦 CI/CD

The project uses GitHub Actions for CI/CD:

- **Tests** run on every PR
- **Code quality** checks (linting, formatting)
- **Security** scanning
- **Documentation** building
- **Deployment** (on main branch)

Ensure your changes pass all CI checks before requesting review.

## 📞 Getting Help

- **GitHub Issues**: For bugs and feature requests
- **GitHub Discussions**: For questions and general discussion
- **Documentation**: Check existing docs first
- **Code Review**: Ask for help in PR comments

## 🎯 Good First Issues

Look for issues labeled `good first issue` if you're new to the project. These are typically:

- Documentation improvements
- Small bug fixes
- Test additions
- Code cleanup tasks

## 📜 Code of Conduct

Please note that this project is released with a Contributor Code of Conduct. By participating in this project you agree to abide by its terms.

## 🙏 Recognition

Contributors will be recognized in:
- README contributors section
- Release notes
- GitHub contributors page

Thank you for contributing to AIrchivist! 🎉
