/**
 * Global application state management using React Context
 */
import React, { createContext, useContext, useReducer, useEffect } from 'react';
import { notesAPI, tagsAPI } from '../services/api';

// Action types
const ActionTypes = {
  // Loading states
  SET_LOADING: 'SET_LOADING',
  
  // Notes
  SET_NOTES: 'SET_NOTES',
  ADD_NOTE: 'ADD_NOTE',
  UPDATE_NOTE: 'UPDATE_NOTE',
  DELETE_NOTE: 'DELETE_NOTE',
  
  // Tags
  SET_TAGS: 'SET_TAGS',
  ADD_TAG: 'ADD_TAG',
  UPDATE_TAG: 'UPDATE_TAG',
  DELETE_TAG: 'DELETE_TAG',
  
  // UI state
  SET_SELECTED_NOTE: 'SET_SELECTED_NOTE',
  SET_SEARCH_QUERY: 'SET_SEARCH_QUERY',
  SET_SELECTED_TAG: 'SET_SELECTED_TAG',
  SET_VIEW_MODE: 'SET_VIEW_MODE',
  
  // Error handling
  SET_ERROR: 'SET_ERROR',
  CLEAR_ERROR: 'CLEAR_ERROR',
  
  // Settings
  SET_SETTINGS: 'SET_SETTINGS',
  UPDATE_SETTING: 'UPDATE_SETTING',
};

// Initial state
const initialState = {
  // Data
  notes: [],
  tags: [],
  
  // Loading states
  loading: {
    notes: false,
    tags: false,
    note: false,
  },
  
  // UI state
  selectedNote: null,
  searchQuery: '',
  selectedTag: null,
  viewMode: 'list', // 'list', 'grid', 'graph'
  
  // Error state
  error: null,
  
  // Settings
  settings: {
    notesDirectory: '',
    llmEnabled: true,
    autoSave: true,
    theme: 'light',
  },
  
  // Pagination
  pagination: {
    page: 1,
    perPage: 20,
    total: 0,
    hasMore: true,
  },
};

// Reducer function
function appReducer(state, action) {
  switch (action.type) {
    case ActionTypes.SET_LOADING:
      return {
        ...state,
        loading: {
          ...state.loading,
          [action.payload.key]: action.payload.value,
        },
      };

    case ActionTypes.SET_NOTES:
      return {
        ...state,
        notes: action.payload.notes,
        pagination: action.payload.pagination || state.pagination,
      };

    case ActionTypes.ADD_NOTE:
      return {
        ...state,
        notes: [action.payload, ...state.notes],
      };

    case ActionTypes.UPDATE_NOTE:
      return {
        ...state,
        notes: state.notes.map(note =>
          note.id === action.payload.id ? { ...note, ...action.payload } : note
        ),
        selectedNote: state.selectedNote?.id === action.payload.id
          ? { ...state.selectedNote, ...action.payload }
          : state.selectedNote,
      };

    case ActionTypes.DELETE_NOTE:
      return {
        ...state,
        notes: state.notes.filter(note => note.id !== action.payload),
        selectedNote: state.selectedNote?.id === action.payload ? null : state.selectedNote,
      };

    case ActionTypes.SET_TAGS:
      return {
        ...state,
        tags: action.payload,
      };

    case ActionTypes.ADD_TAG:
      return {
        ...state,
        tags: [...state.tags, action.payload],
      };

    case ActionTypes.UPDATE_TAG:
      return {
        ...state,
        tags: state.tags.map(tag =>
          tag.id === action.payload.id ? { ...tag, ...action.payload } : tag
        ),
      };

    case ActionTypes.DELETE_TAG:
      return {
        ...state,
        tags: state.tags.filter(tag => tag.id !== action.payload),
      };

    case ActionTypes.SET_SELECTED_NOTE:
      return {
        ...state,
        selectedNote: action.payload,
      };

    case ActionTypes.SET_SEARCH_QUERY:
      return {
        ...state,
        searchQuery: action.payload,
      };

    case ActionTypes.SET_SELECTED_TAG:
      return {
        ...state,
        selectedTag: action.payload,
      };

    case ActionTypes.SET_VIEW_MODE:
      return {
        ...state,
        viewMode: action.payload,
      };

    case ActionTypes.SET_ERROR:
      return {
        ...state,
        error: action.payload,
      };

    case ActionTypes.CLEAR_ERROR:
      return {
        ...state,
        error: null,
      };

    case ActionTypes.SET_SETTINGS:
      return {
        ...state,
        settings: action.payload,
      };

    case ActionTypes.UPDATE_SETTING:
      return {
        ...state,
        settings: {
          ...state.settings,
          [action.payload.key]: action.payload.value,
        },
      };

    default:
      return state;
  }
}

// Create contexts
const AppStateContext = createContext();
const AppDispatchContext = createContext();

// Provider component
export function AppProvider({ children }) {
  const [state, dispatch] = useReducer(appReducer, initialState);

  // Load initial data
  useEffect(() => {
    loadInitialData();
  }, []);

  const loadInitialData = async () => {
    try {
      // Load tags
      dispatch({ type: ActionTypes.SET_LOADING, payload: { key: 'tags', value: true } });
      const tagsResponse = await tagsAPI.getTags();
      if (tagsResponse.success) {
        dispatch({ type: ActionTypes.SET_TAGS, payload: tagsResponse.data });
      }
    } catch (error) {
      dispatch({ type: ActionTypes.SET_ERROR, payload: error.message });
    } finally {
      dispatch({ type: ActionTypes.SET_LOADING, payload: { key: 'tags', value: false } });
    }

    try {
      // Load notes directory setting
      const directoryResponse = await notesAPI.getNotesDirectory();
      if (directoryResponse.success) {
        dispatch({
          type: ActionTypes.UPDATE_SETTING,
          payload: { key: 'notesDirectory', value: directoryResponse.data.notes_directory },
        });
      }
    } catch (error) {
      console.warn('Failed to load notes directory:', error);
    }
  };

  return (
    <AppStateContext.Provider value={state}>
      <AppDispatchContext.Provider value={dispatch}>
        {children}
      </AppDispatchContext.Provider>
    </AppStateContext.Provider>
  );
}

// Custom hooks to use the context
export function useAppState() {
  const context = useContext(AppStateContext);
  if (context === undefined) {
    throw new Error('useAppState must be used within an AppProvider');
  }
  return context;
}

export function useAppDispatch() {
  const context = useContext(AppDispatchContext);
  if (context === undefined) {
    throw new Error('useAppDispatch must be used within an AppProvider');
  }
  return context;
}

// Combined hook for convenience
export function useApp() {
  return {
    state: useAppState(),
    dispatch: useAppDispatch(),
  };
}

// Action creators for common operations
export const actions = {
  setLoading: (key, value) => ({
    type: ActionTypes.SET_LOADING,
    payload: { key, value },
  }),

  setNotes: (notes, pagination) => ({
    type: ActionTypes.SET_NOTES,
    payload: { notes, pagination },
  }),

  addNote: (note) => ({
    type: ActionTypes.ADD_NOTE,
    payload: note,
  }),

  updateNote: (note) => ({
    type: ActionTypes.UPDATE_NOTE,
    payload: note,
  }),

  deleteNote: (noteId) => ({
    type: ActionTypes.DELETE_NOTE,
    payload: noteId,
  }),

  setTags: (tags) => ({
    type: ActionTypes.SET_TAGS,
    payload: tags,
  }),

  addTag: (tag) => ({
    type: ActionTypes.ADD_TAG,
    payload: tag,
  }),

  updateTag: (tag) => ({
    type: ActionTypes.UPDATE_TAG,
    payload: tag,
  }),

  deleteTag: (tagId) => ({
    type: ActionTypes.DELETE_TAG,
    payload: tagId,
  }),

  setSelectedNote: (note) => ({
    type: ActionTypes.SET_SELECTED_NOTE,
    payload: note,
  }),

  setSearchQuery: (query) => ({
    type: ActionTypes.SET_SEARCH_QUERY,
    payload: query,
  }),

  setSelectedTag: (tag) => ({
    type: ActionTypes.SET_SELECTED_TAG,
    payload: tag,
  }),

  setViewMode: (mode) => ({
    type: ActionTypes.SET_VIEW_MODE,
    payload: mode,
  }),

  setError: (error) => ({
    type: ActionTypes.SET_ERROR,
    payload: error,
  }),

  clearError: () => ({
    type: ActionTypes.CLEAR_ERROR,
  }),

  setSettings: (settings) => ({
    type: ActionTypes.SET_SETTINGS,
    payload: settings,
  }),

  updateSetting: (key, value) => ({
    type: ActionTypes.UPDATE_SETTING,
    payload: { key, value },
  }),
};

export { ActionTypes };
