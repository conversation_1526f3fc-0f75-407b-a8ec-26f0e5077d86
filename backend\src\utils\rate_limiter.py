"""
Rate limiting utilities for API endpoints
"""
import time
from collections import defaultdict, deque
from functools import wraps
from typing import Dict, Deque, <PERSON><PERSON>
from flask import request, g

from src.utils.logging_config import get_logger
from src.utils.response_formatter import error_response

logger = get_logger('airchivist.rate_limiter')


class RateLimiter:
    """
    Simple in-memory rate limiter using sliding window
    """
    
    def __init__(self):
        # Store request timestamps for each client
        self._requests: Dict[str, Deque[float]] = defaultdict(deque)
    
    def is_allowed(self, client_id: str, limit: int, window_seconds: int) -> Tuple[bool, Dict[str, int]]:
        """
        Check if request is allowed under rate limit
        
        Args:
            client_id: Unique identifier for the client
            limit: Maximum number of requests allowed
            window_seconds: Time window in seconds
            
        Returns:
            Tuple of (is_allowed, rate_limit_info)
        """
        now = time.time()
        window_start = now - window_seconds
        
        # Get request history for this client
        requests = self._requests[client_id]
        
        # Remove old requests outside the window
        while requests and requests[0] < window_start:
            requests.popleft()
        
        # Check if under limit
        current_count = len(requests)
        is_allowed = current_count < limit
        
        if is_allowed:
            # Add current request
            requests.append(now)
        
        # Calculate rate limit info
        remaining = max(0, limit - current_count - (1 if is_allowed else 0))
        reset_time = int(requests[0] + window_seconds) if requests else int(now + window_seconds)
        
        rate_limit_info = {
            'limit': limit,
            'remaining': remaining,
            'reset': reset_time,
            'window': window_seconds
        }
        
        return is_allowed, rate_limit_info
    
    def cleanup_old_entries(self, max_age_seconds: int = 3600):
        """
        Clean up old entries to prevent memory leaks
        
        Args:
            max_age_seconds: Maximum age of entries to keep
        """
        now = time.time()
        cutoff = now - max_age_seconds
        
        # Remove clients with no recent requests
        clients_to_remove = []
        for client_id, requests in self._requests.items():
            # Remove old requests
            while requests and requests[0] < cutoff:
                requests.popleft()
            
            # If no requests left, mark for removal
            if not requests:
                clients_to_remove.append(client_id)
        
        for client_id in clients_to_remove:
            del self._requests[client_id]
        
        logger.debug(f"Cleaned up {len(clients_to_remove)} old rate limit entries")


# Global rate limiter instance
_rate_limiter = RateLimiter()


def get_client_id() -> str:
    """
    Get unique client identifier for rate limiting
    
    Returns:
        Client identifier string
    """
    # Use IP address as client identifier
    # In production, you might want to use authenticated user ID
    return request.environ.get('HTTP_X_FORWARDED_FOR', request.remote_addr)


def rate_limit(requests_per_minute: int = 60, requests_per_hour: int = 1000):
    """
    Decorator to apply rate limiting to endpoints
    
    Args:
        requests_per_minute: Maximum requests per minute
        requests_per_hour: Maximum requests per hour
        
    Returns:
        Decorator function
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            client_id = get_client_id()
            
            # Check minute limit
            allowed_minute, info_minute = _rate_limiter.is_allowed(
                f"{client_id}:minute", requests_per_minute, 60
            )
            
            # Check hour limit
            allowed_hour, info_hour = _rate_limiter.is_allowed(
                f"{client_id}:hour", requests_per_hour, 3600
            )
            
            # Use the more restrictive limit
            if not allowed_minute:
                logger.warning(f"Rate limit exceeded (minute) for client {client_id}")
                return error_response(
                    message="Rate limit exceeded. Too many requests per minute.",
                    status_code=429,
                    meta={
                        'rate_limit': info_minute,
                        'retry_after': info_minute['reset'] - int(time.time())
                    }
                )
            
            if not allowed_hour:
                logger.warning(f"Rate limit exceeded (hour) for client {client_id}")
                return error_response(
                    message="Rate limit exceeded. Too many requests per hour.",
                    status_code=429,
                    meta={
                        'rate_limit': info_hour,
                        'retry_after': info_hour['reset'] - int(time.time())
                    }
                )
            
            # Store rate limit info in Flask's g object for response headers
            g.rate_limit_info = {
                'minute': info_minute,
                'hour': info_hour
            }
            
            return f(*args, **kwargs)
        
        return decorated_function
    return decorator


def strict_rate_limit(requests_per_minute: int = 10):
    """
    Strict rate limiting decorator for sensitive endpoints
    
    Args:
        requests_per_minute: Maximum requests per minute
        
    Returns:
        Decorator function
    """
    return rate_limit(requests_per_minute=requests_per_minute, requests_per_hour=requests_per_minute * 60)


def add_rate_limit_headers(response):
    """
    Add rate limit headers to response
    
    Args:
        response: Flask response object
        
    Returns:
        Modified response with rate limit headers
    """
    if hasattr(g, 'rate_limit_info'):
        info = g.rate_limit_info
        
        # Add minute limit headers
        if 'minute' in info:
            minute_info = info['minute']
            response.headers['X-RateLimit-Limit-Minute'] = str(minute_info['limit'])
            response.headers['X-RateLimit-Remaining-Minute'] = str(minute_info['remaining'])
            response.headers['X-RateLimit-Reset-Minute'] = str(minute_info['reset'])
        
        # Add hour limit headers
        if 'hour' in info:
            hour_info = info['hour']
            response.headers['X-RateLimit-Limit-Hour'] = str(hour_info['limit'])
            response.headers['X-RateLimit-Remaining-Hour'] = str(hour_info['remaining'])
            response.headers['X-RateLimit-Reset-Hour'] = str(hour_info['reset'])
    
    return response


def cleanup_rate_limiter():
    """Clean up old rate limiter entries"""
    _rate_limiter.cleanup_old_entries()
