#!/usr/bin/env python3

import sys

sys.path.insert(0, '/home/<USER>/note-manager-backend')

from flask import Flask
from src.models import Note, db
from src.services.file_service import FileService

# Create Flask app
app = Flask(__name__)
app.config['SQLALCHEMY_DATABASE_URI'] = "sqlite:////home/<USER>/note-manager-backend/src/database/app.db"
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# Initialize database
db.init_app(app)

with app.app_context():
    # Create tables if they don't exist
    db.create_all()
    
    # Test file service
    fs = FileService()
    print(f"Notes directory: {fs.notes_directory}")
    
    # Test importing a single file
    print("\nTesting single file import...")
    note = fs.import_file('/home/<USER>/sample_notes/sample_note1.md', process_with_llm=False)
    if note:
        print(f"Successfully imported: {note.title}")
    else:
        print("Failed to import file")
    
    # Test importing directory
    print("\nTesting directory import...")
    notes = fs.import_directory('/home/<USER>/sample_notes', recursive=True, process_with_llm=False)
    print(f"Imported {len(notes)} notes from directory")
    
    # Check database
    all_notes = Note.query.all()
    print(f"\nTotal notes in database: {len(all_notes)}")
    for note in all_notes:
        print(f"- {note.title} ({note.file_type})")

